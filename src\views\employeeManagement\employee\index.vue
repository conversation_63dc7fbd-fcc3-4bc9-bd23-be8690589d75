<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :rules="queryRules" :inline="true" v-show="showSearch">
      <el-form-item label="关键字" prop="nameOrNumber">
        <el-input v-model="queryParams.nameOrNumber" placeholder="员工姓名/联系电话" style="width: 200px" clearable
          @keyup.enter="handleQuery" maxlength="32" />
      </el-form-item>
      <el-form-item label="岗位类型" prop="jobType">
        <el-select v-model="queryParams.jobType" placeholder="请选择岗位类型" clearable style="width: 200px">
          <el-option v-for="dict in zkss_position" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['employeeManagement:employee:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['employeeManagement:employee:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['employeeManagement:employee:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['employeeManagement:employee:importData']">导入</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['employeeManagement:employee:export']">导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="employeeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="员工姓名" align="center" prop="employeeName" />
      <el-table-column label="联系电话" align="center" prop="phoneNumber" />
      <el-table-column label="岗位类型" align="center" prop="jobType">
        <template #default="scope">
          <dict-tag :options="zkss_position" :value="scope.row.jobType" />
        </template>
      </el-table-column>
      <el-table-column label="详细地址" align="center" prop="address">
        <template #default="scope">
          {{ scope.row.region }}{{ scope.row.address }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" link @click="handleUpdate(scope.row)"
            v-hasPermi="['employeeManagement:employee:edit']">修改</el-button>
          <el-button v-if="scope.row.status != '1' || scope.row.status != 1" type="primary" link
            @click="handleDelete(scope.row)" v-hasPermi="['employeeManagement:employee:remove']">删除</el-button>
          <el-button v-if="scope.row.status === '0' || scope.row.status === 0" type="primary" link
          v-hasPermi="['employeeManagement:employee:status']"
            @click="handleOpenAccount(scope.row)">开通账号</el-button>
          <el-button v-else-if="scope.row.status === '1' || scope.row.status === 1" type="primary" link
          v-hasPermi="['employeeManagement:employee:status']"
             @click="handleDisableAccount(scope.row)">停用账号</el-button>
          <el-button v-else-if="scope.row.status === '2' || scope.row.status === 2" type="primary" link
          v-hasPermi="['employeeManagement:employee:status']"
            @click="handleEnableAccount(scope.row)">启用账号</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改员工对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="代理商" prop="deptId" v-hasPermi="['system:dept:select']">
          <el-tree-select v-model="form.deptId" :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择代理商"
            check-strictly clearable @change="handleDeptChange" />
        </el-form-item>
        <el-form-item label="员工姓名：" prop="employeeName">
          <el-input v-model="form.employeeName" placeholder="请输入员工姓名" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="联系电话" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入联系电话" maxlength="11" />
        </el-form-item>
        <el-form-item label="岗位类型：" prop="jobType">
          <el-select v-model="form.jobType" placeholder="请选择岗位类型" multiple collapse-tags collapse-tags-tooltip>
            <el-option v-for="dict in zkss_position" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域" prop="region">
          <Cascader v-model="singleCode" placeholder="请选择区域" :maxLoadLevel="3" :maxSelectLevel="3"
            @change="handleAreaChangeSingle" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" maxlength="60" style="width: 80%;" />&nbsp;
          <el-input type="button" value="搜索" @click="onSearch" style="width: 10%;" />
          <div id="mapDiv" style="width: 100%;height: 400px;margin-top: 5px;"></div>
        </el-form-item>
        <el-form-item label="设置折扣范围：" prop="discountRate">
          <el-input-number v-model="form.discountRate" placeholder="请输入折扣范围" :precision="0" :min="0"
            :max="deptMaxDiscountRate" :controls="false" style="width: 60%;" />&nbsp;<span>%</span>
        </el-form-item>
        <el-form-item label="员工图片" prop="employeePictureList">
          <image-upload v-model="form.employeePictureList" :limit="1" placeholder="请上传营业执照" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 开通账号弹框 -->
    <el-dialog title="开通账号" v-model="openAccountDialogVisible" width="500px" append-to-body>
      <el-form label-width="60px">
        <el-form-item label="账号：">
          <el-input v-model="openAccountForm.account" disabled />
        </el-form-item>
        <el-form-item label="角色：">
          <el-input v-model="openAccountForm.role" disabled />
        </el-form-item>
        <div style="color: #999; font-size: 13px; margin-bottom: 16px;">
          手机号作为该员工的账号，岗位类型作为其角色，若确认无误，点击确定开通即可。<br>
          若信息有误，可在员工信息里进行修改后再为其开通账号。
        </div>
      </el-form>
      <template #footer>
        <el-button @click="openAccountDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmOpenAccount">确定开通</el-button>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleCheck, VideoPause, Plus, Edit, Delete, VideoPlay, UploadFilled } from '@element-plus/icons-vue'
import { listEmployee, getEmployee, delEmployee, addEmployee, updateEmployee } from "@/api/employee/employee"
import { getDept } from "@/api/system/dept"
import { useDict } from '@/utils/dict'
import useUserStore from '@/store/modules/user'
import Cascader from '@/components/Cascader'
import { deptTreeSelect } from "@/api/common";
import { hasPermi } from '@/utils/index'
const { proxy } = getCurrentInstance()
// 字典数据
const { zkss_position } = useDict(['zkss_position'])
// 获取用户store
const userStore = useUserStore()

// 数据状态
const deptOptions = ref(undefined);
const map = ref(null);
const localsearch = ref('')
const geocoder = ref(null)
const singleCode = ref()
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const total = ref(0)
const employeeList = ref([])
const title = ref("")
const open = ref(false)
const openAccountDialogVisible = ref(false)
const openAccountForm = reactive({
  account: '',
  role: '',
  employeeId: null
})
// 新增：部门最大折扣范围
const deptMaxDiscountRate = ref(100)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  nameOrNumber: null,
  jobType: null,
})

// 查询表单校验规则
const queryRules = reactive({
  nameOrNumber: [
    { max: 32, message: "搜索内容长度不能超过32个字符", trigger: "blur" }
  ]
})

// 表单参数
const form = reactive({
  deptId: null,
  employeeId: null,
  employeeName: null,
  phoneNumber: null,
  jobType: [],
  region: null,
  address: null,
  lat: null,
  lng: null,
  discountRate: null,
  employeePicture: null,
  employeePictureList: null,
  wxOpenId: null,
  userId: null,
  delFlag: null,
  status: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
})

// 表单校验
const rules = reactive({
  deptId: [{ required: true, message: "所属组织不能为空", trigger: "change" }],
  employeeName: [
    { required: true, message: "员工姓名不能为空", trigger: "blur" },
    { max: 20, message: "员工姓名长度不能超过20个字符", trigger: "blur" }
  ],
  phoneNumber: [
    { required: true, message: "联系电话不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的联系电话", trigger: "blur" },
    { max: 11, message: "联系电话长度不能超过11个字符", trigger: "blur" }
  ],
  jobType: [
    { required: true, message: "岗位类型不能为空", trigger: "change" },
  ],
  address: [
    { max: 60, message: "详细地址长度不能超过60个字符", trigger: "blur" }
  ],
  discountRate: [
    { type: 'number', min: 0, max: 100, message: "折扣范围必须在0-100之间", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined) {
          // 检查整数部分不超过3位（因为numeric(5,2)总共5位，小数2位，所以整数最多3位）
          const intPart = Math.floor(Math.abs(value)).toString()
          if (intPart.length > 3) {
            callback(new Error('折扣范围整数部分不能超过3位'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, trigger: "blur"
    }
  ],
  employeePicture: [
    { max: 255, message: "员工图片路径长度不能超过255个字符", trigger: "blur" }
  ],
  lat: [
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined) {
          // 检查整数部分不超过3位（因为numeric(10,7)总共10位，小数7位，所以整数最多3位）
          const intPart = Math.floor(Math.abs(value)).toString()
          if (intPart.length > 3) {
            callback(new Error('纬度整数部分不能超过3位'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, trigger: "blur"
    }
  ],
  lng: [
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined) {
          // 检查整数部分不超过3位（因为numeric(10,7)总共10位，小数7位，所以整数最多3位）
          const intPart = Math.floor(Math.abs(value)).toString()
          if (intPart.length > 3) {
            callback(new Error('经度整数部分不能超过3位'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, trigger: "blur"
    }
  ],
})

// 表单引用
const formRef = ref(null)
const queryForm = ref(null)

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect({ type: "0,1" }).then((response) => {
    deptOptions.value = response.data;
  });
}

/** 查询员工列表 */
const getList = async () => {
  loading.value = true
  try {
    const response = await listEmployee(queryParams)
    employeeList.value = response.rows
    total.value = response.total
  } finally {
    loading.value = false
  }
}

// 表单重置
const reset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryForm.value.resetFields()
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.employeeId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 获取空表单对象
const getEmptyForm = () => ({
  employeeId: null,
  deptId: null,
  employeeName: null,
  phoneNumber: null,
  jobType: [],
  region: null,
  address: null,
  lat: null,
  lng: null,
  discountRate: null,
  employeePicture: null,
  wxOpenId: null,
  userId: null,
  delFlag: null,
  status: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null,
  employeePictureList: null,
})

const handleDeptChange = async (deptId) => {
  try {
    const res = await getDept(deptId)
    form.discountRate = 0
    deptMaxDiscountRate.value = res.data.discountRate || 100
  } catch (e) {
    deptMaxDiscountRate.value = 100
  }
}

/** 新增按钮操作 */
const handleAdd = async () => {
  singleCode.value = null
  // 1. 彻底清空内容
  Object.assign(form, getEmptyForm())
  // 2. 设置当前用户的部门ID
  form.deptId = userStore.deptId || null
  // 获取部门最大折扣范围
  if (form.deptId) {
    try {
      const res = await getDept(form.deptId)
      deptMaxDiscountRate.value = res.data.discountRate || 100
    } catch (e) {
      deptMaxDiscountRate.value = 100
    }
  } else {
    deptMaxDiscountRate.value = 100
  }
  form.jobType = []
  open.value = true
  title.value = "添加员工"
  if (hasPermi('system:dept:select')) {
    getDeptTree();
  }
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
    initMap();
  });
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset()
  const employeeId = row.employeeId || ids.value[0]
  try {
    const response = await getEmployee(employeeId)
    Object.assign(form, response.data)
    singleCode.value = response.data && response.data.regionCascadeId
      ? response.data.regionCascadeId.split(",")
      : [];
    // 回显岗位类型为数组
    if (typeof form.jobType === 'string') {
      form.jobType = form.jobType.split(',').filter(Boolean)
    } else if (!Array.isArray(form.jobType)) {
      form.jobType = []
    }
    open.value = true
    title.value = "修改员工"
    if (hasPermi('system:dept:select')) {
      getDeptTree();
    }
    nextTick(() => {
      initMap();
    });
  } catch (error) {
    console.error(error)
  }
}

/** 提交按钮 */
const submitForm = async () => {
  try {
    await formRef.value.validate()
    // 岗位类型多选，保存时用逗号拼接
    const submitData = { ...form, jobType: Array.isArray(form.jobType) ? form.jobType.join(',') : form.jobType }
    if (form.employeeId != null) {
      await updateEmployee(submitData)
      ElMessage.success("修改成功")
    } else {
      await addEmployee(submitData)
      ElMessage.success("新增成功")
    }
    open.value = false
    getList()
  } catch (error) {
    console.error(error)
  }
}

// 取消按钮
const cancel = () => {
  reset()
  // 清空form所有字段
  Object.keys(form).forEach(key => {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = null
    }
  })
  open.value = false
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const employeeIds = row.employeeId || ids.value
  try {
    await ElMessageBox.confirm(`是否确认删除员工姓名为"${row.employeeName}"的数据项？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delEmployee(employeeIds)
    ElMessage.success("删除成功")
    getList()
  } catch (error) {
    if (error != "cancel") {
      ElMessage.error("删除失败")
    }
  }
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download("employeeManagement/employee/export", {
    ...queryParams
  }, `employee_${new Date().getTime()}.xlsx`)
}

/** 开通账号 */
const handleOpenAccount = (row) => {
  openAccountForm.account = row.phoneNumber
  // 处理多选岗位类型，将逗号分隔的字符串转换为对应的标签名称
  let roleLabels = []
  if (row.jobType) {
    const jobTypeArray = row.jobType.split(',').filter(Boolean)
    roleLabels = jobTypeArray.map(jobType => {
      const dictItem = (zkss_position.value || []).find(item => item.value == jobType)
      return dictItem ? dictItem.label : jobType
    })
  }
  openAccountForm.role = roleLabels.join('、')
  openAccountForm.employeeId = row.employeeId
  openAccountDialogVisible.value = true
}

/** 确定开通 */
const confirmOpenAccount = async () => {
  await updateEmployee({ employeeId: openAccountForm.employeeId, status: 1 })
  openAccountDialogVisible.value = false
  ElMessage.success('账号已开通')
  getList()
}

/** 停用账号 */
const handleDisableAccount = (row) => {
  ElMessageBox.confirm(
    '确认是否停用该账号？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await updateEmployee({ employeeId: row.employeeId, status: 2 })
    ElMessage.success('账号已停用')
    getList()
  }).catch(() => { })
}

/** 启用账号 */
const handleEnableAccount = (row) => {
  ElMessageBox.confirm(
    '确认是否启用该账号？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await updateEmployee({ employeeId: row.employeeId, status: 1 })
    ElMessage.success('账号已启用')
    getList()
  }).catch(() => { })
}

// 导入相关状态
const uploadRef = ref(null)
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/employeeManagement/employee/importData",
  updateSupport: 0
})

/** 导入 */
function handleImport() {
  upload.title = "员工导入"
  upload.open = true
}

/** 导入模板 */
function importTemplate() {
  proxy.download("employeeManagement/employee/importTemplate", {}, `员工导入模板_${new Date().getTime()}.xlsx`)
}

/** 导入成功 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

/** 导入相关方法 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs["uploadRef"].handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}

//选择区域
function handleAreaChangeSingle(value) {
  if (value != null && value != '') {
    form.region = value[value.length - 1]
  }

}

//初始化地图
function initMap() {
  if (map.value) {
    map.value.clearOverLays()
  } else {
    var imageURL =
      "https://t0.tianditu.gov.cn/vec_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    const imageURLT =
      "https://t0.tianditu.gov.cn/cia_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
      "&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    var lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
    var lay2 = new T.TileLayer(imageURLT, { minZoom: 1, maxZoom: 18 });
    const config = { layers: [lay, lay2] };
    map.value = new T.Map("mapDiv");
    map.value.enableScrollWheelZoom();
    map.value.enableDrag()
    geocoder.value = new T.Geocoder();
    map.value.addEventListener("click", MapClick);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function (position) {
          map.value.centerAndZoom(new T.LngLat(position.coords.longitude, position.coords.latitude), 14);
        },
        function (error) {
        }
      );
    }
    var searchConfig = {
      pageCapacity: 10,	//每页显示的数量
      onSearchComplete: localSearchResult	//接收数据的回调函数
    };
    //创建搜索对象
    setTimeout(() => {
      localsearch.value = new T.LocalSearch(map.value, searchConfig);
    })
  }
  if (form.lng && form.lat) {
    map.value.centerAndZoom(new T.LngLat(form.lng, form.lat), 14);
    //创建标注对象
    var marker = new T.Marker(new T.LngLat(form.lng, form.lat));
    //向地图上添加标注
    map.value.addOverLay(marker);
  } else {
    map.value.centerAndZoom(new T.LngLat(proxy.centerPointLng, proxy.centerPointLat), 11);
  }
}

//地图点击事件
function MapClick(e) {
  map.value.clearOverLays()
  var marker = new T.Marker(new T.LngLat(e.lnglat.getLng(), e.lnglat.getLat()));
  //向地图上添加标注
  map.value.addOverLay(marker);
  form.lng = e.lnglat.getLng()
  form.lat = e.lnglat.getLat()
  const geocode = new T.Geocoder();
  geocode.getLocation(e.lnglat, function (result) {
    const town = result.addressComponent.town
    const index = result.addressComponent.address.indexOf(town);
    if (index !== -1) {
      form.address = result.addressComponent.address.slice(index + town.length);
    } else {
      form.address = result.addressComponent.poi
    }
  });
}

function localSearchResult(result) {
  map.value.clearOverLays();
  if (result.getPois().length > 0) {
    const resultType = result.resultType
    let center = null
    if (resultType == 1) {
      center = result.pois[0].lonlat
    } else if (resultType == 2) {
      center = result.getStatistics().priorityCitys[0].lonlat
    } else if (resultType == 3) {
      const info = result.getArea()
      center = info.lonlat
    }
    if (center) {
      const arr = center.split(",")
      map.value.panTo(new T.LngLat(arr[0], arr[1]));
      var marker = new T.Marker(new T.LngLat(arr[0], arr[1]));
      map.value.addOverLay(marker);
    }
  }
}

function onSearch() {
  localsearch.value.search(form.address)
}

// 生命周期钩子
onMounted(() => {
  if (!userStore.deptId) {
    userStore.getInfo().then(() => {
      getList()
    }).catch(() => {
      getList()
    })
  } else {
    getList()
  }
})
</script>

<style scoped></style>
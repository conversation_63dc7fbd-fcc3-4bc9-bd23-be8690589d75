import request from '@/utils/request'

// 查询员工列表
export function listEmployee(query) {
  return request({
    url: '/employeeManagement/employee/list',
    method: 'get',
    params: query
  })
}

// 查询员工详细
export function getEmployee(employeeId) {
  return request({
    url: '/employeeManagement/employee/' + employeeId,
    method: 'get'
  })
}

// 新增员工
export function addEmployee(data) {
  return request({
    url: '/employeeManagement/employee',
    method: 'post',
    data: data
  })
}

// 修改员工
export function updateEmployee(data) {
  return request({
    url: '/employeeManagement/employee',
    method: 'put',
    data: data
  })
}

// 删除员工
export function delEmployee(employeeId) {
  return request({
    url: '/employeeManagement/employee/' + employeeId,
    method: 'delete'
  })
}

// 获取员工下拉选项
export function getEmployeeDropdown(query) {
  return request({
    url: '/employeeManagement/employee/dropdown',
    method: 'get',
    params: query
  })
}

<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true" label-width="68px">
         <el-form-item label="关键字" prop="keyWord">
            <el-input
               v-model="queryParams.keyWord"
               placeholder="请输入套餐名称/套餐编号"
               clearable
               style="width: 240px"
            />
         </el-form-item>
         <el-form-item label="作物类型" prop="plantTypeId">
            <el-select
               v-model="queryParams.plantTypeId"
               placeholder="作物类型"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in plant_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item label="创建时间" style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD"
               type="daterange"
               range-separator="-"
               start-placeholder="开始时间"
               end-placeholder="结束时间"
            ></el-date-picker>
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select
               v-model="queryParams.status"
               placeholder="状态"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="obj in statusList"
                  :key="obj.value"
                  :label="obj.label"
                  :value="obj.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['business:combination:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['business:combination:remove']"
            >删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 表格数据 -->
      <el-table v-loading="loading" :data="combinationList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="序号" prop="seqNumber" width="55" />
         <el-table-column label="套餐名称" prop="name" :show-overflow-tooltip="true"/>
         <el-table-column label="套餐编号" prop="number" :show-overflow-tooltip="true"/>
        <el-table-column prop="plantTypeName" label="作物类型">
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
                <el-button link type="primary"   @click="handleCopy(scope.row)" v-hasPermi="['business:combination:copy']">复制</el-button>
                <el-button v-if="scope.row.status == 2" link type="primary"   @click="doActivate(scope.row)" v-hasPermi="['business:combination:enable']">启用</el-button>
                <el-button v-if="scope.row.status == 1" link type="primary"   @click="doDisActivate(scope.row)" v-hasPermi="['business:combination:disable']">停用</el-button>
                <el-button v-if="scope.row.status == 2" link type="primary"   @click="handleDelete(scope.row)" v-hasPermi="['business:combination:remove']">删除</el-button>
              <el-button link type="primary"  @click="viewDetail(scope.row)" v-hasPermi="['business:combination:view']">查看详情</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <AddDialog ref="addDialogRef" @handleQuery="handleQuery"></AddDialog>
      <CombinationDetail ref="combinationDetailRef"></CombinationDetail>
   </div>
</template>

<script setup name="Combination">
import AddDialog from './components/AddDialog.vue'
import CombinationDetail from '@/components/CombinationDetail/index.vue'
import { listCombination, getCombination, addCombination,delCombination,disActivate,activate } from "@/api/business-management/combination"
const router = useRouter()
const { proxy } = getCurrentInstance()
const { plant_type } = proxy.useDict("plant_type")
const combinationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])
const combinationDetail = ref({})
const combinationDetailRef=ref(null)
const statusList = ref([
      {
   "label":"正常",
   "value":"1"
   },
      {
   "label":"停用",
   "value":"2"
   }
]) 


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    plantTypeId: undefined,
    dateRange: undefined
  },
  rules: {
    // roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }]
  },
})

const { queryParams, form, rules } = toRefs(data)
const addDialogRef = ref(null)
/** 查询套餐列表 */
function getList() {
  loading.value = true
  listCombination(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    combinationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  const combinationIds = row.id || ids.value
  proxy.$modal.confirm('确认删除?').then(function () {
    return delCombination(combinationIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function doDisActivate(row) {
  proxy.$modal.confirm('确认停用?').then(function () {
    return disActivate(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("停用成功")
  }).catch(() => {})
}

function doActivate(row) {
    activate(row.id)
    getList()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
function handleAdd(){
   addDialogRef.value.isShowDialog = true
}
function handleCopy(row){
   getCombination(row.id).then(response => {
    if(response.code==200){
      addDialogRef.value.isShowDialog = true
      addDialogRef.value.formModel = response.data
    }
  })
}
function viewDetail(row){
   getCombination(row.id).then(response => {
    if(response.code==200){
      combinationDetailRef.value.setMenuData(response.data)  
    }
  })
}
getList()
</script>

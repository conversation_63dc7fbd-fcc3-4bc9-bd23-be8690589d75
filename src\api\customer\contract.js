import request from '@/utils/request'

// 查询客户合同列表
export function listCustomerContracts(customerId) {
  return request({
    url: '/customerManagement/contract/list/' + customerId,
    method: 'get'
  })
}

// 查询合同详情
export function getContractDetail(contractId) {
  return request({
    url: '/customerManagement/contract/detail/' + contractId,
    method: 'get'
  })
}

// 新增合同
export function addContract(data) {
  return request({
    url: '/customerManagement/contract/add',
    method: 'post',
    data: data
  })
}

// 修改合同
export function updateContract(data) {
  return request({
    url: '/customerManagement/contract/update',
    method: 'put',
    data: data
  })
}

// 删除合同
export function deleteContract(contractId) {
  return request({
    url: '/customerManagement/contract/delete/' + contractId,
    method: 'delete'
  })
}

// 下载合同附件
export function downloadContractAttachment(contractId) {
  return request({
    url: '/customerManagement/contract/download/' + contractId,
    method: 'get',
    responseType: 'blob'
  })
} 
// 所属种植户村集体
export function getAllGrowerAndVillage() {
  return request({
    url: '/customerManagement/customer/getAllGrowerAndVillage',
    method: 'get',  
  })
} 
// 所属种植户
export function employeeDropdown(params) {
  return request({
    url: '/employeeManagement/employee/dropdown',
    method: 'get', 
    params, 
  })
} 
// 合同识别
export function orderRecognition(data) {
  return request({
    url: '/order/orderRecognition',
    method: 'post', 
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
} 
// 创建订单
export function orderCreate(data) {
  return request({
    url: '/order/orderCreate',
    method: 'post',  
    data: data
  })
} 

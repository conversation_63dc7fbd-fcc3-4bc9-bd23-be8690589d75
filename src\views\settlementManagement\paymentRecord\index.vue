<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="关键字" prop="searchText">
            <el-input
               v-model="queryParams.searchText"
               placeholder="凭证上传人员姓名/联系电话"
               clearable
               style="width: 200px;"
            />
         </el-form-item>        
         <el-form-item label="付款日期"  style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD"
               type="daterange"
               range-separator="-"
               start-placeholder="开始时间"
               end-placeholder="结束时间"
            ></el-date-picker>
         </el-form-item>
         <el-form-item label="状态"   prop="status">
            <el-select
               v-model="queryParams.status"
               placeholder="全部"
               clearable
               style="width: 120px"
            >
               <el-option
                  v-for="dict in pc_payment_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>         
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-table  v-loading="loading" :data="paymentRecordList">
         <el-table-column label="序号" align="center" prop="seqNumber"  width="55"/>
         <el-table-column label="付款日期" align="center" prop="paymentDate" :show-overflow-tooltip="true" />
         <el-table-column label="应付金额（元）" align="center" prop="paymentAmount">
         </el-table-column>
         <el-table-column label="付款凭证" align="center" prop="paymentPicList">
            <template #default="scope">
               <div class="image-container">
                 <el-image  preview-teleported="true"  fit="cover"  style="width: 120px; height: 80px;margin-right: 10px;" v-for="(img,index) in scope.row.paymentPicList" :src="img" :preview-src-list="getImgList(scope.row.paymentPicList,index)"></el-image>               
               </div>
             </template>
         </el-table-column>
         <el-table-column label="凭证上传人员" align="center" prop="createNameAndPhone"  :show-overflow-tooltip="true" />
         <el-table-column label="状态" align="center" prop="statusName" :show-overflow-tooltip="true" />
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary"  @click="showPaymentRecord(scope.row)" v-hasPermi="['settlementmanagement:paymentrecord:view']">付款明细</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <paymentRecordDetail ref="paymentRecordDetailRef"></paymentRecordDetail>
   </div>
</template>

<script setup name="Order">
import { listPaymentRecord, getPaymentRecordDetailByRecordId } from "@/api/settlement-management/payment-record/payment-record"
import paymentRecordDetail from "./components/paymentRecordDetail.vue"
const { proxy } = getCurrentInstance()
const { pc_payment_status } = proxy.useDict("pc_payment_status")
const paymentRecordList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const multiple = ref(false)
const total = ref(0)
const dateRange = ref([])
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    searchText: undefined
  }
})

const { queryParams, form } = toRefs(data)
const paymentRecordDetailRef = ref(null)
/** 查询订单列表 */
function getList() {
  loading.value = true
  queryParams.value.createName=queryParams.value.searchText
  queryParams.value.phone=queryParams.value.searchText
  listPaymentRecord(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    paymentRecordList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  queryParams.value.pageNum = 1
}

/** 付款明细按钮操作 */
function showPaymentRecord(row) {
   paymentRecordDetailRef.value.openDetail(row)
}

function getImgList(workPhoto, index) {
let arr = []
  	let i = 0;
	for(i;i < workPhoto.length;i++){
	  arr.push(workPhoto[i+index])
	  if(i+index >= workPhoto.length-1){
	    index = 0-(i+1);
	  }
	}
  return arr;
    };

getList()
</script>
<style  scoped lang="scss">
.image-container {
  display: flex;
  flex-direction: row; /* 确保是横向排列 */
  align-items: center; /* 垂直居中对齐 */
}
</style>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键字" prop="searchValue">
        <el-input v-model="queryParams.searchValue" placeholder="请输入代理商名称/联系人/联系电话" clearable @keyup.enter="handleQuery"
          style="width: 300px;" />
      </el-form-item>
      <el-form-item label="服务区域" prop="serviceArea" style="width: 380px;">
        <Cascader v-model="queryMultipleCode" placeholder="请选择区域" :multiple="true" :checkStrictly="true" :maxLoadLevel="3" :maxSelectLevel="3"
          @change="handleAreaChangeMultipQuery" style="width: 100%;" :collapseTags="true" :collapseTagsTooltip="true" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:agent:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:agent:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:agent:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="agentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="代理商名称" align="center" prop="deptName">
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="leader">
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phone">
      </el-table-column>
      <el-table-column label="服务区域" align="center" prop="serviceAreaNames">
      </el-table-column>
      <el-table-column label="详细地址" align="center" prop="address">
        <template #default="scope">
          {{ scope.row.region }}{{ scope.row.address }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:agent:edit']">修改</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)"
            v-hasPermi="['system:agent:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改代理商对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form ref="agentRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属组织" prop="parentId" v-if="hasPermi('system:dept:select')">
          <el-tree-select v-model="form.parentId" :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择所属组织"
            check-strictly clearable />
        </el-form-item>
        <el-form-item label="代理商名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入代理商名称" :maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="联系人" prop="leader">
          <el-input v-model="form.leader" placeholder="请输入联系人" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" show-word-limit />
        </el-form-item>
        <el-form-item label="服务区域" prop="serviceArea">
          <!-- 多选 -->
          <Cascader v-model="multipleCode" placeholder="请选择多个地区" :checkStrictly="true" :multiple="true" :maxLoadLevel="3" :maxSelectLevel="3"
            @change="handleAreaChangeMultip" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="办公地址" prop="areaCascadeId">
          <!-- 单选 -->
          <Cascader v-model="singleCode" placeholder="请选择区域" :maxLoadLevel="3" :maxSelectLevel="3"
            @change="handleAreaChangeSingle" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="" prop="address">
          <el-input v-model="form.address" placeholder="请输入办公地址" style="width: 80%;" />&nbsp;
          <el-input type="button" value="搜索" @click="onSearch" style="width: 10%;" />
          <div id="agentMap" style="width: 100%;height: 400px;margin-top: 5px;"></div>
        </el-form-item>
        <el-form-item label="设置折扣范围" prop="discountRate">
          <el-input-number v-model="form.discountRate" placeholder="请输入折扣范围" :precision="0" :min="0" :max="100"
            :controls="false" style="width: 80%;" />&nbsp;<span>%</span>
        </el-form-item>
        <el-form-item label="信用代码" prop="creditCode">
          <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" maxlength="18" style="width: 80%;"
            show-word-limit />
        </el-form-item>
        <el-form-item label="营业执照" prop="businessLicensePicList">
          <image-upload v-model="form.businessLicensePicList" :fileSize="10" :limit="1" placeholder="请上传营业执照" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Agent">
import { listAgent, getAgent, delAgent, addAgent, updateAgent } from "@/api/system/agent"
import { deptTreeSelect } from "@/api/common";
import Cascader from '@/components/Cascader'
import { hasPermi } from '@/utils'
const { proxy } = getCurrentInstance()

const agentList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const deptNames = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const map = ref(null);
const geocoder = ref(null)
const localsearch = ref('')
const agentRef = ref(null)
const deptOptions = ref(undefined);
const singleCode = ref()
const multipleCode = ref([])
const queryMultipleCode = ref([])
const mapMarker=ref(null)
const data = reactive({
  form: {
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    searchValue: null,
    serviceArea: null,
    deptType: '1' // 代理商
  },
  rules: {
    parentId: [{ required: true, message: "所属组织不能为空", trigger: "change" }],
    deptName: [{ required: true, message: "代理商名称不能为空", trigger: "blur" }],
    leader: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
    phone: [{ required: true, message: "联系电话不能为空", trigger: "blur" },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }],
    serviceArea: [{ required: true, message: "服务区域不能为空", trigger: "change" }],
    creditCode: [{ pattern: /^[A-HJ-NPQRTUWXY0-9]{18}$/, message: "请输入正确的信用代码", trigger: "blur" }],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询代理商列表 */
function getList() {
  loading.value = true
  listAgent(queryParams.value).then(response => {
    agentList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    deptId: null,
    parentId: null,
    ancestors: null,
    deptName: null,
    orderNum: null,
    leader: null,
    phone: null,
    email: null,
    status: null,
    delFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    deptType: "1",
    region: null,
    address: null,
    lng: null,
    lat: null,
    serviceArea: null,
    discountRate: null,
    creditCode: null,
    businessLicensePic: null,
    businessLicensePicList: null
  }
  proxy.resetForm("agentRef")
  singleCode.value = []
  multipleCode.value = []
}

function handleAreaChangeMultipQuery(value) {
  if (Array.isArray(value) && value.length > 0) {
    // 提取每个子数组的最后一个元素
    const lastElements = value.map(sublist => sublist[sublist.length - 1]);
    // 用逗号拼接这些元素
    const result = lastElements.join(',');
    queryParams.value.serviceArea = result
  }
  handleQuery()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  queryMultipleCode.value = []
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.deptId)
  deptNames.value = selection.map(item => item.deptName)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect({ type: 0 }).then((response) => {
    deptOptions.value = response.data;
  });
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "新增代理商"
  getDeptTree();
  nextTick(() => {
    initMap();
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  getDeptTree()
  const _deptId = row.deptId || ids.value
  getAgent(_deptId).then(response => {
    form.value = response.data
    singleCode.value = response.data && response.data.regionCascadeId
      ? response.data.regionCascadeId.split(",")
      : [];
    multipleCode.value = response.data.serviceAreaList
    open.value = true
    title.value = "修改代理商"
    nextTick(() => {
      initMap();
    });
  })
}

function handleAreaChangeSingle(value) {
  if (value != null && value != '') {
    form.value.region = value[value.length - 1]
  } else {
    form.value.region = ""
  }
  console.log('单选值:', value)
}

function handleAreaChangeMultip(value) {
  console.log(294444,multipleCode)
  if (Array.isArray(value) && value.length > 0) {
    // 提取每个子数组的最后一个元素
    const lastElements = value.map(sublist => sublist[sublist.length - 1]);
    // 用逗号拼接这些元素
    const result = lastElements.join(',');
    form.value.serviceArea = result
  } else {
    form.value.serviceArea = ""
  }
  console.log('多选值:', value)
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["agentRef"].validate(valid => {
    if (valid) {
      singleCode.value = null
      multipleCode.value = null
      console.log(form.value)
      if (form.value.deptId != null) {
        updateAgent(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addAgent(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _deptIds = row.deptId || ids.value
  const _deptNames = row.deptName || deptNames.value
  proxy.$modal.confirm('是否确认删除代理商名称为"' + _deptNames + '"的数据项？').then(function () {
    return delAgent(_deptIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

function initMap() {
  if (map.value) {
    map.value.clearOverLays()
  } else {
    var imageURL =
      "https://t0.tianditu.gov.cn/vec_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    const imageURLT =
      "https://t0.tianditu.gov.cn/cia_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
      "&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    var lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
    var lay2 = new T.TileLayer(imageURLT, { minZoom: 1, maxZoom: 18 });
    const config = { layers: [lay, lay2] };
    map.value = new T.Map("agentMap");
    map.value.enableScrollWheelZoom();
    map.value.enableDrag()
    geocoder.value = new T.Geocoder();
    map.value.addEventListener("click", MapClick);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function (position) {
          map.value.centerAndZoom(new T.LngLat(position.coords.longitude, position.coords.latitude), 14);
        },
        function (error) {
        }
      );
    }
    var searchConfig = {
      pageCapacity: 10,	//每页显示的数量
      onSearchComplete: localSearchResult	//接收数据的回调函数
    };
    //创建搜索对象
    setTimeout(() => {
      localsearch.value = new T.LocalSearch(map.value, searchConfig);
    })
  }
  if (form.value.lng && form.value.lat) {
    map.value.centerAndZoom(new T.LngLat(form.value.lng, form.value.lat), 14);
    setmapMarker(new T.LngLat(form.value.lng, form.value.lat)) 
  } else {
    map.value.centerAndZoom(new T.LngLat(proxy.centerPointLng, proxy.centerPointLat), 11);
  }
}

function MapClick(e) {
  map.value.clearOverLays() 
  mapMarker.value = null
  setmapMarker(new T.LngLat(e.lnglat.getLng(), e.lnglat.getLat()))
  form.value.lng = e.lnglat.getLng()
  form.value.lat = e.lnglat.getLat()
  const geocode = new T.Geocoder();
  geocode.getLocation(e.lnglat, function (result) {
    console.log('result', result.addressComponent)
    console.log('formatted_address', result.formatted_address)
    const town = result.addressComponent.town
    const index = result.addressComponent.address.indexOf(town);
    if (index !== -1) {
      form.value.address = result.addressComponent.address.slice(index + town.length);
    } else {
      form.value.address = result.addressComponent.poi
    }
  });
}

function onSearch() {
  localsearch.value.search(form.value.address)
}

function localSearchResult(result) {
  if (result.getPois().length > 0) {
    const resultType = result.resultType
    let center = null
    if (resultType == 1) {
      center = result.pois[0].lonlat
    } else if (resultType == 2) {
      center = result.getStatistics().priorityCitys[0].lonlat
    } else if (resultType == 3) {
      const info = result.getArea()
      center = info.lonlat
    }
    if (center) { 
      const arr = center.split(",")
      map.value.panTo(new T.LngLat(arr[0], arr[1]));
      setmapMarker(new T.LngLat(arr[0], arr[1]))
    }
  }
}
function setmapMarker(point){ 
  if(mapMarker.value){ 
        mapMarker.value.setLngLat(point);
      }else{ 
        mapMarker.value = new T.Marker(point);
        map.value.addOverLay(mapMarker.value);
      } 
}
function searchResult(result) {
  if (result.getStatus() == 0) {
    map.value.panTo(result.getLocationPoint());
    //创建标注对象
    // var marker = new T.Marker(result.getLocationPoint());
    // //向地图上添加标注
    // map.value.addOverLay(marker);
    // console.log(result.getLocationPoint());
  } else {
    alert(result.getMsg());
  }

}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('agent/agent/export', {
    ...queryParams.value
  }, `agent_${new Date().getTime()}.xlsx`)
}

getList()
</script>

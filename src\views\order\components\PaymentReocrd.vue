<template>
    <div>
        <el-dialog v-model="isShowPayRecord" title="付款记录" width="1000" @close="close" append-to-body>
            <div class="top-money">
                金额：{{ contractPrice }}元，已付金额：{{ paidAmount }}元，未付金额：{{ contractPrice - paidAmount }}元
            </div>
            <div  style="padding-left: 50px;">
                <el-timeline style="max-width: 600px">
                    <el-timeline-item v-for="(item, index) in timeList" :key="index"
                        >
                        <div v-if="index==0" style="color: #fff;width:50px;padding: 5px;text-align:center;border-radius: 20px;background: red;margin-bottom: 8px;">最新</div>
                        <div class="label-value">
                            <div class="label">付款时间</div>
                            <div class="value">{{ item.createTime }} </div>
                        </div>
                        <div class="label-value">
                            <div class="label">付款内容</div>
                            <div class="value">{{ item.servicePhaseName }} </div>
                        </div>
                        <div class="label-value">
                            <div class="label">付款金额</div>
                            <div class="value">{{ item.paymentAmount }}元 </div>
                        </div>
                        <div class="label-value" v-if="item.attachments.length">
                            <div class="label">付款凭证</div>
                            <div class="value">                              
                                <!-- <el-image width="10px" height="10px" :preview-teleported="true" ref="imageRef" style="width: 100px; height: 100px"
                                    :src="item.attachments[0]" show-progress :preview-src-list="item.attachments"
                                    fit="cover" /> -->
                                <el-image v-for="(ojb,index) in item.attachments" 
                                            :src="ojb" 
                                            style="width: 100px; height: 100px" 
                                            :preview-src-list="getImgList(item.attachments, index)" />                             
                            </div>
                        </div>
                        <div class="label-value">
                            <div class="label">上传人</div>
                            <div class="value">{{ item.createName }} </div>
                        </div>
                        <div class="label-value">
                            <div class="label">核验状态</div>
                            <div class="value">{{ item.statusName }} </div>
                        </div>
                        <div class="label-value" v-if="item.verifyName">
                            <div class="label">核验人</div>
                            <div class="value">{{ item.verifyName }} </div>
                        </div>
                        <div class="label-value" v-if="item.verifyTime">
                            <div class="label">核验时间</div>
                            <div class="value">{{ item.verifyTime }} </div>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, computed, nextTick, onMounted, watch, defineExpose, defineEmits, defineProps } from "vue";
import { paymentRecord } from "@/api/order-management/order"
const isShowPayRecord = ref(false);
const contractPrice = ref(0);
const paidAmount = ref(0);
const timeList = ref([])
function getPaymentRecord(id) {
    paymentRecord({ id }).then(res => {
        timeList.value = res.data
    })
};
function getImgList(workPhoto, index) {
      let arr = []
      if (workPhoto.length == 1) {
        arr.push(workPhoto[0])
      } else if (workPhoto.length == 0)  {
        return arr;
      } else {
        for(let i = 0;i < workPhoto.length;i++){
          arr.push(workPhoto[i+index])
          if(i+index >= workPhoto.length-1){
            index = 0-(i+1);
          }
        }
      }
      return arr;
    };

defineExpose({ isShowPayRecord, paidAmount, contractPrice, getPaymentRecord });
</script>
<style scoped lang="scss">
.top-money{
    position: relative;
    padding-left: 20px;
    margin-bottom: 20px;
}
.top-money::before{
    content: "";
    position: absolute;
    left: 10px;
    width: 5px;
    height: 20px;
    background: #00923f;
}
.label-value{
    display: flex;
    margin-bottom:10px;

}
.label-value .label{
    width:80px;
    text-align: center;
    line-height: 30px;
    height: 30px;
    border-radius: 16px;
    background: #D9EFE2;
    color: #00923f;
    margin-right: 8px;
}
.label-value  .value{
    position: relative;
    top: 4px;
}
</style>

<template>
  <div>
    <el-dialog v-model="isShowDialog" :title="title" width="1000" @close="close" append-to-body>
      <el-form :model="formModel" ref="formRef">
        <el-form-item label="套餐名称" prop="name" :rules="[{ required: true, message: '请输入套餐名称,必须是唯一,不允许重复', trigger: 'blur' }]">
          <el-input style="width: 300px;" v-model="formModel.name" placeholder="请输入套餐名称,必须是唯一,不允许重复" />
        </el-form-item>
        <el-form-item label="作物类型" prop="plantTypeId"
          :rules="[{ required: true, message: '请选择作物类型', trigger: 'blur' }]">
          <el-select style="width: 300px;" v-model="formModel.plantTypeId" placeholder="请选择作物类型">
            <el-option v-for="dict in plant_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐编号" prop="number" :rules="[{ required: true, message: '请输入套餐编号', trigger: 'blur' }]">
          <el-input style="width: 300px;" readonly v-model="formModel.number" placeholder="请输入套餐编号" />
        </el-form-item>
        <el-table :data="flatcombinationPhaseList" border style="width: 100%" :span-method="tableSpanMethod">
          <!-- 阶段操作列 -->
          <el-table-column label="">
            <template #default="scope">
              <div v-if="scope.row._isFirstInGroup">
                <el-button v-if="formModel.combinationPhaseList.length > 1" type="text" style="color:#f88"
                  @click="removeStage(scope.row._groupIndex)">删除阶段</el-button>
                <el-button type="text" @click="addStage(scope.row._groupIndex)">新增阶段</el-button>
              </div>
            </template>
          </el-table-column>
          <!-- 阶段名称 -->
          <el-table-column label="服务阶段" width="100">
            <template #default="scope">
              <span v-if="scope.row._isFirstInGroup">第{{ convertToChineseNumber(scope.row._groupIndex+1 )}}阶段</span>
            </template>
          </el-table-column>
          <!-- 类型 必填 -->
          <el-table-column label="类型*" width="200">
            <template #header>
              <span>类型</span>
              <span style="color:red;margin-left: 5px;">*</span>
            </template>
            <template #default="scope">
              <el-form-item
                :prop="`combinationPhaseList.${scope.row._groupIndex}.combinationPhaseDetailList.${scope.row._rowIndex}.typeId`"
                :rules="[{ required: true, message: '请选择类型', trigger: 'change' }]" style="margin-bottom:0"
                :validate-on-rule-change="false" :key="`type-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                <el-select
                  v-model="formModel.combinationPhaseList[scope.row._groupIndex].combinationPhaseDetailList[scope.row._rowIndex].typeId"
                  placeholder="请选择类型"
                  @change="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'typeId')">
                  <el-option v-for="dict in combination_service_phase_type" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- 服务内容 必填 -->
          <el-table-column label="服务内容*" width="200">
            <template #header>
              <span>服务内容</span>
              <span style="color:red;margin-left: 5px;">*</span>
            </template>
            <template #default="scope">
              <el-form-item
                :prop="`combinationPhaseList.${scope.row._groupIndex}.combinationPhaseDetailList.${scope.row._rowIndex}.serviceContent`"
                :rules="[{ required: true, message: '请填写服务内容', trigger: 'blur' }]" style="margin-bottom:0"
                :validate-on-rule-change="false" :key="`content-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                <el-input
                  v-model="formModel.combinationPhaseList[scope.row._groupIndex].combinationPhaseDetailList[scope.row._rowIndex].serviceContent"
                  placeholder="请填写服务内容"
                  @blur="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'serviceContent')" />
              </el-form-item>
            </template>
          </el-table-column>
          <!-- 作业标准 必填 -->
          <el-table-column label="作业标准*" width="200">
            <template #header>
              <span>作业标准</span>
              <span style="color:red;margin-left: 5px;">*</span>
            </template>
            <template #default="scope">
              <el-form-item
                :prop="`combinationPhaseList.${scope.row._groupIndex}.combinationPhaseDetailList.${scope.row._rowIndex}.standard`"
                :rules="[{ required: true, message: '请填写作业标准', trigger: 'blur' }]" style="margin-bottom:0"
                :validate-on-rule-change="false" :key="`standard-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                <el-input
                  v-model="formModel.combinationPhaseList[scope.row._groupIndex].combinationPhaseDetailList[scope.row._rowIndex].standard"
                  placeholder="请填写作业标准"
                  @blur="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'standard')" />
              </el-form-item>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-icon color="#409EFF" size="20" style="cursor: pointer;"
                @click="addRow(scope.row._groupIndex, scope.row._rowIndex)">
                <CirclePlusFilled />
              </el-icon>
              <el-icon color="#F56C6C" size="20" style="cursor: pointer;"
                v-if="formModel.combinationPhaseList[scope.row._groupIndex].combinationPhaseDetailList.length > 1"
                @click="removeRow(scope.row._groupIndex, scope.row._rowIndex)">
                <RemoveFilled />
              </el-icon>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import { convertToChineseNumber } from '@/utils/index'
import { addCombination,newNumber } from '@/api/business-management/combination'
const { proxy } = getCurrentInstance()
const emit = defineEmits(['handleQuery'])
const { plant_type, combination_service_phase_type } = proxy.useDict("plant_type", "combination_service_phase_type")
const isShowDialog = ref(false)
const title = ref('新增套餐')
const formRef = ref(null)
const formModel = ref({
  name: '',
  number: '',
  plantTypeId: '',
  combinationPhaseList: [
    { 
      combinationPhaseDetailList: [
        { typeId: '', serviceContent: '', standard: '' },
      ]
    }
  ]
})
watch(()=>formModel.value.plantTypeId,()=>{
  if(formModel.value.plantTypeId){
    newNumber({plantTypeId:formModel.value.plantTypeId}).then(res=>{
      formModel.value.number=res.data
    })
  }
})
function reorderStages() {
  formModel.value.combinationPhaseList.forEach((item, idx) => {
    item.stage = `第${convertToChineseNumber(idx + 1)}阶段`
  })
}

const flatcombinationPhaseList = computed(() => {
  console.log(11)
  const arr = []
  formModel.value.combinationPhaseList.forEach((group, groupIdx) => {
    group.combinationPhaseDetailList.forEach((item, rowIdx) => {
      arr.push({
        ...item, 
        _groupIndex: groupIdx,
        _rowIndex: rowIdx,
        _isFirstInGroup: rowIdx === 0,
        _groupCount: group.combinationPhaseDetailList.length
      })
    })
  })
  return arr
})

function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 操作列和服务阶段列
  if (columnIndex === 0 || columnIndex === 1) {
    if (row._isFirstInGroup) {
      return { rowspan: row._groupCount, colspan: 1 }
    } else {
      return { rowspan: 0, colspan: 0 }
    }
  }
}

function removeStage(groupIdx) {
  if (formModel.value.combinationPhaseList.length === 1) {
    ElMessage.warning('至少保留一个阶段')
    return
  }
  formModel.value.combinationPhaseList.splice(groupIdx, 1)
  // reorderStages()
}

function addStage(groupIdx) {
  formModel.value.combinationPhaseList.splice(groupIdx + 1, 0, { 
    combinationPhaseDetailList: [
      { typeId: '', serviceContent: '', standard: '' }
    ]
  })
  // reorderStages()
}

function removeRow(groupIdx, rowIdx) {
  const group = formModel.value.combinationPhaseList[groupIdx]
  if (group.combinationPhaseDetailList.length === 1) {
    ElMessage.warning('每个阶段至少保留一行')
    return
  }
  group.combinationPhaseDetailList.splice(rowIdx, 1)
}

function addRow(groupIdx, rowIdx) {
  formModel.value.combinationPhaseList[groupIdx].combinationPhaseDetailList.splice(rowIdx + 1, 0, { typeId: '', serviceContent: '', standard: '' })
}
function close() {
  isShowDialog.value = false
}

function handleFieldChange(groupIdx, rowIdx, field) {
  if (formRef.value) {
    formRef.value.clearValidate(`combinationPhaseList.${groupIdx}.combinationPhaseDetailList.${rowIdx}.${field}`)
    formRef.value.validateField(`combinationPhaseList.${groupIdx}.combinationPhaseDetailList.${rowIdx}.${field}`)
  }
}
function cancel() { 
  resetForm()
  isShowDialog.value = false
}
function resetForm() {
  formModel.value = {
    name: '',
    number: '',
    plantTypeId: '',
    combinationPhaseList: [
      { 
        combinationPhaseDetailList: [
          { typeId: '', serviceContent: '', standard: '' },
        ]
      }
    ]
  }
  formRef.value.resetFields() 
}
function handleSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      formModel.value.combinationPhaseList.forEach((item, index) => {
        item.servicePhase = index + 1
      })
      console.log(222000, formModel.value)
      addCombination(formModel.value).then(res => {
        if (res.code === 200) {
          resetForm()
          proxy.$modal.msgSuccess("新增成功")
          emit('handleQuery')
          isShowDialog.value = false
        }
      })
    } else {
      proxy.$modal.msgError('请完善所有必填项！')
    }
  })
}
defineExpose({ isShowDialog,formModel })

</script>
<style scoped lang="scss"></style>
<template>
    <div>
        <el-dialog v-model="isShowDialog" title="付款明细" width="1200" @close="close" append-to-body>
            <el-row :ugutter="20">
                <el-col :span="8">
                    <div class="left-info">
                        <div class="paymentDate-paymentAmount">
                            <img src="@/assets/images/time.png" alt="">
                            <span>付款日期：{{ info.paymentDate }}</span>
                        </div>
                        <div class="paymentDate-paymentAmount">
                            <img src="@/assets/images/money.png" alt="">
                            <span>应付金额：{{ info.paymentAmount }}元</span>
                        </div>
                        <div class="paymentDate-paymentAmount">
                            <img src="@/assets/images/uploadPerson.png" alt="">
                            <span>上传人员：{{ info.createName }}</span>
                        </div>
                        <div style="margin-bottom: 10px;">付款凭证</div>
                        <el-image preview-teleported="true" fit="cover"
                            style="width: 60px; height: 40px;margin-right: 10px;"
                            v-for="(img, index) in info.paymentPicList" :key="index" :src="img"
                            :preview-src-list="info.paymentPicList"></el-image>

                    </div>
                </el-col>
                <el-col :span="16">
                    <div class="right-table">
                        <div style="margin-bottom: 10px;">
                            <el-input v-model="searchText" @keyup.enter="getDetail"
                                placeholder="请输入订单编号/服务对象名称/所属业务员/联系电话" clearable
                                style="width: 300px;margin-right: 10px;" />
                            <el-button type="primary" icon="Search" @click="getDetail">查询</el-button>
                        </div>
                        <div v-for="(item, index) in detailList" :key="index" class="order-item">
                            <div class="order-header">
                                <span >订单编号: {{ item.orderNumber }}</span>
                                <span class="farmer-name">
                                    <el-icon><UserFilled /></el-icon>
                                    {{ item.farmerName }}
                                </span>
                                <span  >{{ item.plantTypeName }}</span>
                                <span class="create-time">{{ item.createTime }}</span>
                                <span class="employee-info">{{ item.employeeName }} ({{ item.employeePhone }})</span>
                            </div>
                            <table class="menu-table">
                                <thead>
                                    <tr>
                                        <th  >付款内容</th>
                                        <th  >金额</th>  
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="(group, groupIndex) in item.orderPhasePaymentResList"
                                        :key="groupIndex">
                                        <tr>
                                            <td>{{ group.servicePhaseName }}</td>
                                            <td>{{ group.payPrice }}</td>
                                            <td>
                                                <el-button 
                                                    type="primary" 
                                                    link 
                                                    @click="toggleServiceContent(index, groupIndex)"
                                                >
                                                    查看服务内容>
                                                </el-button>
                                            </td>
                                        </tr>
                                        <!-- 展开的服务内容详情 -->
                                        <tr v-if="group.isOpen" class="service-detail-row">
                                            <td colspan="3" class="service-detail-content">
                                                <div class="service-detail-header">
                                                    <span>服务内容</span>
                                                </div>
                                                <table class="service-detail-table">
                                                    <thead>
                                                        <tr>
                                                            <th width="80">序号</th>
                                                            <th>服务内容</th>
                                                            <th>操作标准</th>
                                                            <th width="120">市场价格(元)</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr 
                                                            v-for="(detail, detailIndex) in group.orderPhaseDetailList" 
                                                            :key="detailIndex"
                                                        >
                                                            <td>{{ detail.seqNumber }}</td>
                                                            <td>{{ detail.serviceContent }}</td>
                                                            <td>{{ detail.standard }}</td>
                                                            <td>{{ detail.marketPrice }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                        <div style="text-align: center;">
                            <el-button   type="danger"  @click="getDetail">核验不通过</el-button>
                            <el-button type="primary"  >核验通过</el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>

        </el-dialog>
    </div>
</template>
<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import { getPaymentRecordDetailByRecordId } from "@/api/settlement-management/payment-record/payment-record"
import { get } from '@vueuse/core'
import { OfficeBuilding } from '@element-plus/icons-vue'
const isShowDialog = ref(false)
const info = ref({})
const detailList = ref([])
const searchText = ref('')
function close() {
    isShowDialog.value = false
}
function openDetail(row) {
    isShowDialog.value = true
    info.value = row
    getDetail()
}
function getDetail() {
    getPaymentRecordDetailByRecordId(info.value.id, searchText.value).then(res => {
        res.data.forEach(item => {
            item.orderPhasePaymentResList.forEach(group => {
                group.isOpen = false
            })
        })
        detailList.value = res.data
    })
}

function toggleServiceContent(orderIndex, groupIndex) {
    detailList.value[orderIndex].orderPhasePaymentResList[groupIndex].isOpen = 
        !detailList.value[orderIndex].orderPhasePaymentResList[groupIndex].isOpen
}
defineExpose({
    openDetail
})
</script>
<style scoped lang="scss">
.left-info {
    padding: 20px;
    width: 100%;

    .paymentDate-paymentAmount {
        padding: 10px;
        padding-left: 20px;
        width: 100%;
        background: #F3FAF6;
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        img {
            width: 15px;
            height: 15px;
            margin-right: 10px;
        }
    }
}

.right-table {
    padding: 20px;

    .order-item {
        margin-bottom: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        overflow: hidden;

        .order-header {
            padding: 15px 20px;
            background: #e9ecef;
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 14px;
            color: #495057;

            .order-number {
                font-weight: bold;
                color: #007bff;
            }

            .farmer-name {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .plant-type {
                background: #28a745;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
            }

            .create-time {
                color: #6c757d;
            }

            .employee-info {
                color: #495057;
            }
        }

        .menu-table {
            width: 100%;
            border-collapse: collapse;

            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #dee2e6;
            }

            th {
                background: #f8f9fa;
                font-weight: 600;
                color: #495057;
            }

            td {
                background: white;
            }

            .service-detail-row {
                .service-detail-content {
                    padding: 0;
                    background: #f8f9fa;
                }

                .service-detail-header {
                    padding: 10px 15px;
                    background: #e9ecef;
                    font-weight: 600;
                    color: #495057;
                    border-bottom: 1px solid #dee2e6;
                }

                .service-detail-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0;

                    th, td {
                        padding: 12px 15px;
                        text-align: left;
                        border-bottom: 1px solid #dee2e6;
                        background: white;
                    }

                    th {
                        background: #f8f9fa;
                        font-weight: 600;
                        color: #495057;
                        border-bottom: 2px solid #dee2e6;
                    }

                    td {
                        color: #495057;
                    }

                    tbody tr:hover {
                        background: #f8f9fa;
                    }
                }
            }
        }
    }
}
</style>
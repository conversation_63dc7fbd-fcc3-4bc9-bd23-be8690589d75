<template>
    <div>
        <el-dialog v-model="isShowDetail" title="订单详情" width="1000" @close="close" append-to-body>
            <el-row>
                <el-col :span="8">
                    <div class="text-center">
                        <span>订单编号</span>
                        <span>{{ info.combinationId }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>市场金额价</span>
                        <span>{{ info.marketPrice }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>服务对象</span>
                        <span>{{ info.farmerName }}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <div class="text-center">
                        <span>订单作物</span>
                        <span>{{ info.plantTypeName }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>折扣金额</span>
                        <span>{{ info.discountPrice }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>所属业务员</span>
                        <span>{{ info.employeeName }}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <div class="text-center">
                        <span>创建时间</span>
                        <span>{{ info.createTime }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>合同金额</span>
                        <span>{{ info.contractPrice }}</span>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="text-center">
                        <span>合同附件</span>
                        <ImagePreview width="50px" height="50px" :src="item.filePath"
                            v-for="(item, index) in info.orderAttachments" :key="index"></ImagePreview>
                    </div>
                </el-col>
            </el-row>
            <div>
                <div class="dialog-title">
                    <div></div>
                    服务内容
                </div>
                <div style="display: flex;justify-content: space-between;">
                    <span>金额：{{ info.contractPrice }}元，已付：{{ info.paidAmount ? info.paidAmount : 0 }}，未付：{{
                        info.contractPrice - info.paidAmount ? info.contractPrice -info.paidAmount : 0 }}元</span>
                    <span>{{ info.combinationName }}</span>
                </div>
            </div>
            <el-table :data="tableData" border style="width: 100%" :span-method="tableSpanMethod1"
                @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" ref="tableRef">
                <el-table-column label="服务阶段" width="100">
                    <template #default="scope">
                        <span>第{{ convertToChineseNumber(scope.row._groupIndex + 1) }}阶段</span>
                    </template>
                </el-table-column>

                <el-table-column label="序号" width="60" align="center">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column prop="serviceContent" label="服务内容" min-width="120" />
                <el-table-column prop="marketPrice" label="市场价（元/亩）" min-width="120" align="center" />
                <el-table-column prop="standard" label="作业标准" min-width="180" />
                <el-table-column prop="statusName" label="状态" />
                <el-table-column prop="standard" label="确认单">
                    <template #default="scope">
                        <el-image
                        :preview-teleported="true"
                            v-if="scope.row._isFirstInGroup && scope.row.confirmFileList && scope.row.confirmFileList.length"
                            ref="imageRef"
                            style="width: 100px; height: 100px"
                            :src="scope.row.confirmFileList[0]"
                            show-progress
                            :preview-src-list="scope.row.confirmFileList"
                            fit="cover"
                        />
                        <div v-else>暂无</div>
                    </template>
                </el-table-column>
                <el-table-column prop="standard" label="付款凭证">
                    <template #default="scope">
                        <el-image
                        :preview-teleported="true"
                            v-if="scope.row._isFirstInGroup && scope.row.paymentFileList && scope.row.paymentFileList.length"
                            ref="imageRef"
                            style="width: 100px; height: 100px"
                            :src="scope.row.paymentFileList[0]"
                            show-progress
                            :preview-src-list="scope.row.paymentFileList"
                            fit="cover"
                        />
                        <div v-else>暂无</div>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch, defineExpose, defineEmits, defineProps } from "vue";
import { orderDetail } from "@/api/business-management/combination"
import { convertToChineseNumber } from '@/utils/index'
const isShowDetail = ref(false)
const info = ref({})
const tableData = ref([])
function getDetail(id) {
    orderDetail({ id }).then(res => {
        console.log(res)
        info.value = res.data
        tableData.value = []
        res.data.orderPhaseList.forEach((group, groupIdx) => {
            group.orderPhaseDetailList.forEach((item, rowIdx) => {
                tableData.value.push({
                    ...item,
                    stage: rowIdx === 0 ? group.stage : '',
                    _groupIndex: groupIdx,
                    _rowIndex: rowIdx,
                    _isFirstInGroup: rowIdx === 0,
                    _groupCount: group.orderPhaseDetailList.length,
                    statusName: group.statusName,
                    paymentFileList: group.paymentFileList,
                    confirmFileList: group.confirmFileList,
                })
            })
        })
    })
}
// 合并"服务阶段"单元格
function tableSpanMethod1({ row, column, rowIndex, columnIndex }) {
    // 需要合并的列索引：服务阶段、状态、确认单、付款凭证
    const mergeColumns = [0, 5, 6, 7];
    if (mergeColumns.includes(columnIndex)) {
        if (row._isFirstInGroup) {
            return {
                rowspan: row._groupCount,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
    // 其他列不合并
}

defineExpose({ isShowDetail, getDetail });
</script>

<style scoped lang="scss">
.el-row {
    margin-bottom: 16px;
}

.dialog-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    div {
        width: 5px;
        height: 25px;
        background: #1DA0A8;
        margin-right: 10px;
    }
}

.text-center {
    display: flex;
    align-items: flex-start;

    span:nth-child(1) {
        display: block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        border-radius: 50px;
        background: #1DA0A8;
        color: #fff;
        margin-right: 10px;
    }

    span:nth-child(2) {
        position: relative;
        top: 5px;
    }
}
</style>
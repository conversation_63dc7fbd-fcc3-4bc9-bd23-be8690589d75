<template>
    <div>
        <el-dialog v-model="isShowDialog" :title="title" width="1000" @close="close" append-to-body>
            <!-- <div class="title">{{ menuData.name }}</div> -->
            <div>
                <table>
                    <tr style="height: 40px;">
                       <td>套餐名称：{{ menuData.name }}</td>
                    </tr>
                    <tr style="height: 40px;">
                        <td>作物类型：{{ menuData.plantTypeName }}</td>
                    </tr>
                    <tr style="height: 40px;">
                        <td>套餐编号：{{ menuData.number }}</td>
                    </tr>
                </table>
            </div>

                <!-- {{ menuData.number }}</div> -->
            <div class="table-container">
                <table class="menu-table">
                    <thead>
                        <tr>
                            <th width="100">服务阶段</th>
                            <th width="100">类型</th>
                            <!-- <th width="60">序号</th> -->
                            <th>服务内容</th>
                            <!-- <th width="120">市场价（元）</th> -->
                            <th>作业标准</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="(group, groupIndex) in menuData.combinationPhaseList" :key="groupIndex">
                            <template v-for="(item, itemIndex) in group.combinationPhaseDetailList" :key="`${groupIndex}-${itemIndex}`">
                                <tr>
                                    <td v-if="itemIndex === 0" :rowspan="group.combinationPhaseDetailList.length" class="stage-cell">
                                        第{{ convertToChineseNumber(groupIndex+1) }}阶段
                                    </td>
                                    <!-- <td class="checkbox-cell"> -->
                                        <!-- <el-checkbox v-model="item.selected" disabled></el-checkbox> -->
                                    <!-- </td> -->
                                    <!-- <td class="id-cell">{{ item.id }}</td> -->
                                     <td>{{ item.typeName }}</td>
                                    <td>{{ item.serviceContent }}</td>
                                    <!-- <td class="price-cell">{{ item.price }}</td> -->
                                    <td>{{ item.standard }}</td>
                                </tr>
                            </template>
                        </template>
                    </tbody>
                    <!-- <tfoot>
                        <tr class="total-row">
                            <td colspan="1" class="total-label">合计</td>
                            <td colspan="1"  ></td>
                            <td colspan="1" ></td>
                            <td colspan="1"  ></td>
                             <td colspan="4" class="total-price"> </td>
                        </tr>
                        <tr class="area-row">
                            <td colspan="2" class="area-label">服务面积（亩）</td>
                            <td class="area-value" colspan="4"> </td>
                        </tr>
                    </tfoot> -->
                </table>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, defineExpose, onMounted, computed } from 'vue'
import { convertToChineseNumber } from '@/utils/index'
const isShowDialog = ref(false) // 默认不显示，只有在接收到数据时才显示
const { proxy } = getCurrentInstance()  
const { plant_type, combination_service_phase_type } = proxy.useDict("plant_type", "combination_service_phase_type")
const title = ref('查看套餐')
const menuData = ref({
    serverArea: '',
    marketAmount: 0,
    tableData: []
})

// 计算选中项的总价
const totalSelectedPrice = computed(() => {
    let total = 0;
    if (menuData.value.combinationPhaseList) {
        menuData.value.combinationPhaseList.forEach(group => {
            group.combinationPhaseDetailList.forEach(item => {
                if (item.selected) {
                    total += parseFloat(item.price) || 0;
                }
            });
        });
    }
    return total;
});

function close(){
    isShowDialog.value = false
}

// 设置菜单数据
function setMenuData(data) {
    console.log(data)
    menuData.value = data
    // 显示对话框
    isShowDialog.value = true
}
defineExpose({
    isShowDialog,
    setMenuData
})
</script>
<style scoped lang="scss">
.title {
    width: 100%;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

.menu-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #EBEEF5;
    
    th, td {
        border: 1px solid #EBEEF5;
        padding: 12px;
        text-align: left;
    }
    
    th {
        background-color: #F5F7FA;
        color: #606266;
        font-weight: 500;
        text-align: center;
    }
    
    .stage-cell {
        text-align: center;
        vertical-align: middle;
    }
    
    .checkbox-cell {
        text-align: center;
    }
    
    .id-cell {
        text-align: center;
    }
    
    .price-cell {
        text-align: center;
    }
    
    .total-row {
        .total-label {
            text-align: right;
            font-weight: bold;
            padding-right: 12px;
        }
        .total-price {
            text-align: center;
            font-weight: bold;
        }
    }
    
    .area-row {
        .area-label {
            text-align: right;
            font-weight: bold;
            padding-right: 12px;
        }
        .area-value {
            text-align: center;
        }
    }
}
</style>
import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/order/list',
    method: 'get',
    params: query
  })
}

// 查看订单详情
export function getOrderDetail(id) {
  return request({
    url: '/order/detail/' + id,
    method: 'get'
  })
}

// 查看订单付款记录
export function getOrderPaymentRecord(id) {
  return request({
    url: '/order/paymentRecord/' + id,
    method: 'get'
  })
}
export function paymentRecord(params) {
  return request({
    url: '/order/paymentRecord',
    method: 'get',
    params,
  })
}
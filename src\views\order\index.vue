<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="关键字" prop="searchText">
            <el-input
               v-model="queryParams.searchText"
               placeholder="请输入订单编号/服务对象"
               clearable
               style="width: 200px;"
            />
         </el-form-item>
         <el-form-item  label="订单作物"  prop="plantTypeId">
            <el-select
               placeholder="全部"
               v-model="queryParams.plantTypeId"
               clearable
               style="width: 120px"
            >
               <el-option
                  v-for="dict in plant_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>         
         <el-form-item label="创建时间"  style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD"
               type="daterange"
               range-separator="-"
               start-placeholder="开始时间"
               end-placeholder="结束时间"
            ></el-date-picker>
         </el-form-item>
         <el-form-item label="状态"   prop="status">
            <el-select
               v-model="queryParams.status"
               placeholder="全部"
               clearable
               style="width: 120px"
            >
               <el-option
                  v-for="dict in order_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>         
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               :disabled="multiple"
               @click="handleUploadContract"
               v-hasPermi="['order:uploadContract']"
            >上传合同</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table  v-loading="loading" :data="orderList">
         <el-table-column  width="55" label="序号" align="center" prop="seqNumber" />
         <el-table-column label="订单编号" align="center" prop="orderNumber" :show-overflow-tooltip="true" />
         <el-table-column label="服务对象" align="center" prop="serviceObject">
         </el-table-column>
         <el-table-column label="订单作物" align="center" prop="plantTypeName" />
         <el-table-column label="市场价金额（元）" align="center" prop="marketPrice" width="110" :show-overflow-tooltip="true" />
         <el-table-column label="折扣金额（元）" align="center" prop="discountPrice" width="130" :show-overflow-tooltip="true" />
         <el-table-column label="合同金额（元）" align="center" prop="contractPrice" width="130" :show-overflow-tooltip="true" />
         <el-table-column label="创建时间" align="center" prop="createTime">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="statusName">
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary"  @click="handleView(scope.row)" v-hasPermi="['order:view']">查看订单</el-button>
               <el-button v-if="scope.row.status !== 1" link type="primary"   @click="showPaymentReocrd(scope.row)" v-hasPermi="['order:paymentRecord']">付款记录</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />
      <AddContract ref="addContractRef" @addContractSuccess="getList"></AddContract>
      <orderDetail ref="orderDetailRef" ></orderDetail>
      <PaymentReocrd ref="paymentReocrdRef"></PaymentReocrd>
   </div>
</template>

<script setup name="Order">
import { listOrder, getOrderDetail, getOrderPaymentRecord } from "@/api/order-management/order"
import orderDetail from "./components/orderDetail.vue"
import AddContract from "@/components/AddContract/index.vue"
import PaymentReocrd from "./components/PaymentReocrd.vue"
const { proxy } = getCurrentInstance()
const { plant_type } = proxy.useDict("plant_type")
const { order_status } = proxy.useDict("order_status")
const orderList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const multiple = ref(false)
const total = ref(0)
const dateRange = ref([])
const orderDetailRef=ref(null)
const paymentReocrdRef=ref(null)
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    searchText: undefined,
    plantTypeId: undefined
  }
})

const { queryParams, form } = toRefs(data)
const addContractRef=ref(null)
/** 查询订单列表 */
function getList() {
  loading.value = true
  queryParams.value.serviceObject=queryParams.value.searchText
  queryParams.value.orderNumber=queryParams.value.searchText
  listOrder(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    orderList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  queryParams.value.pageNum = 1
  handleQuery()
}

/** 详细按钮操作 */
function handleView(row) {
   orderDetailRef.value.isShowDetail=true
   orderDetailRef.value.getDetail(row.id)
}

/** 付款记录按钮操作 */
function showPaymentReocrd(row) {
   paymentReocrdRef.value.isShowPayRecord=true
   paymentReocrdRef.value.contractPrice=row.contractPrice
   paymentReocrdRef.value.paidAmount=row.paidAmount
   paymentReocrdRef.value.getPaymentRecord(row.id)
}
function handleUploadContract(){
   addContractRef.value.isShowAddDialog=true
}
getList()
</script>

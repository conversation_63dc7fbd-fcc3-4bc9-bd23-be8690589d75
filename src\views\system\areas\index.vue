<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键字" prop="searchValue">
        <el-input v-model="queryParams.searchValue" placeholder="请输入区域编码、名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:areas:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:areas:edit']">修改</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="areasList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id">
      </el-table-column>
      <el-table-column label="区域编码" align="center" prop="code">
      </el-table-column>
      <el-table-column label="父级区域编码" align="center" prop="parentCode">
      </el-table-column>
      <el-table-column label="简写区域编码" align="center" prop="shortCode">
      </el-table-column>
      <el-table-column label="父级简写区域编码" align="center" prop="parentShortCode">
      </el-table-column>
      <el-table-column label="串联id" align="center" prop="cascadeId">
      </el-table-column>
      <el-table-column label="名称" align="center" prop="name">
      </el-table-column>
      <el-table-column label="经度" align="center" prop="longitude">
      </el-table-column>
      <el-table-column label="纬度" align="center" prop="latitude">
      </el-table-column>
      <el-table-column label="等级 省1 市2 县3 乡4 村5" align="center" prop="grade">
      </el-table-column>
      <el-table-column label="省" align="center" prop="province">
      </el-table-column>
      <el-table-column label="市" align="center" prop="city">
      </el-table-column>
      <el-table-column label="县/区" align="center" prop="district">
      </el-table-column>
      <el-table-column label="乡/镇/街道" align="center" prop="town">
      </el-table-column>
      <el-table-column label="行政区划全称" align="center" prop="fullName">
      </el-table-column>
      <el-table-column label="是否最下级节点" align="center" prop="isLeaf">
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:areas:edit']">修改</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)"
            v-hasPermi="['system:areas:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改行政区划-省市县乡村五级(2016版本)对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="areasRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="区域编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入区域编码" />
        </el-form-item>
        <el-form-item label="父级编码" prop="parentCode">
          <el-input v-model="form.parentCode" placeholder="请输入父级区域编码" />
        </el-form-item>
        <el-form-item label="简写编码" prop="shortCode">
          <el-input v-model="form.shortCode" placeholder="请输入简写区域编码" />
        </el-form-item>
        <el-form-item label="父级简写编码" prop="parentShortCode">
          <el-input v-model="form.parentShortCode" placeholder="请输入父级简写区域编码" />
        </el-form-item>
        <el-form-item label="串联ID" prop="cascadeId">
          <el-input v-model="form.cascadeId" placeholder="请输入串联ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="省" prop="province">
          <el-input v-model="form.province" placeholder="请输入省" />
        </el-form-item>
        <el-form-item label="市" prop="city">
          <el-input v-model="form.city" placeholder="请输入市" />
        </el-form-item>
        <el-form-item label="县/区" prop="district">
          <el-input v-model="form.district" placeholder="请输入县/区" />
        </el-form-item>
        <el-form-item label="乡/镇/街道" prop="town">
          <el-input v-model="form.town" placeholder="请输入乡/镇/街道" />
        </el-form-item>
        <el-form-item label="村/社区居委会" prop="village">
          <el-input v-model="form.village" placeholder="请输入村/社区居委会" />
        </el-form-item>
        <el-form-item label="行政区划全称" prop="fullName">
          <el-input v-model="form.fullName" placeholder="请输入行政区划全称" />
        </el-form-item>
        <el-form-item label="城乡分类" prop="category">
          <el-input v-model="form.category" placeholder="请输入城乡分类" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Areas">
import { listAreas, getAreas, delAreas, addAreas, updateAreas } from "@/api/system/areas"

const { proxy } = getCurrentInstance()

const areasList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    searchValue: null,
    status: 1
  },
  rules: {
    code: [
      { required: true, message: "区域编码不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询行政区划-省市县乡村五级(2016版本)列表 */
function getList() {
  loading.value = true
  listAreas(queryParams.value).then(response => {
    areasList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    code: null,
    parentCode: null,
    shortCode: null,
    parentShortCode: null,
    cascadeId: null,
    name: null,
    longitude: null,
    latitude: null,
    grade: null,
    province: null,
    city: null,
    district: null,
    town: null,
    village: null,
    fullName: null,
    category: null,
    isLeaf: null,
    status: null,
    memo: null,
    createTime: null
  }
  proxy.resetForm("areasRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加行政区划"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getAreas(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改行政区划"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["areasRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAreas(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addAreas(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除行政区划编号为"' + _ids + '"的数据项？').then(function () {
    return delAreas(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/areas/export', {
    ...queryParams.value
  }, `areas_${new Date().getTime()}.xlsx`)
}

getList()
</script>

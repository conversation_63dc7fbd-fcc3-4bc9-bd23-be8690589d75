<template>
  <div class="login">
    <!-- Logo区域 -->
    <div class="logo-container">
      <div class="logo-content">
        <img src="@/assets/images/login-logo.png" alt="logo" class="logo-img" />
      </div>
    </div>

    <!-- 登录表单区域 -->
    <div class="login-container">
      <div class="login-form-wrapper">
        <!-- 选项卡 -->
        <div class="login-tabs">
          <div class="tab-item active">账号登录</div>
          <div class="tab-item">扫码登录</div>
        </div>

        <!-- 登录表单 -->
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              placeholder="用户"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              placeholder="密码"
              class="custom-input"
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.code"
                size="large"
                auto-complete="off"
                placeholder="验证码"
                class="custom-input captcha-input"
                @keyup.enter="handleLogin"
              />
              <div class="captcha-image">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </div>
          </el-form-item>

          <el-form-item class="remember-me-item">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
          </el-form-item>

          <el-form-item class="login-button-item">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              class="login-button"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
const redirect = ref(undefined)

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 })
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
      } else {
        // 否则移除
        Cookies.remove("username")
        Cookies.remove("password")
        Cookies.remove("rememberMe")
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        router.push({ path: redirect.value || "/", query: otherQueryParams })
      }).catch(() => {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  }
}

getCode()
getCookie()
</script>

<style lang='scss' scoped>
.login {
  position: relative;
  height: 100vh;
  width: 100vw;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
}

// Logo区域样式
.logo-container {
  position: absolute;
  top: 40px;
  left: 40px;
  z-index: 10;

  .logo-content {
    display: flex;
    align-items: center;
    // background: rgba(255, 255, 255, 0.15);
    // backdrop-filter: blur(10px);
    // border-radius: 12px;
    // padding: 16px 24px;
    // border: 1px solid rgba(255, 255, 255, 0.2);

    .logo-img {
      width: 48px;
      height: 48px;
      margin-right: 16px;
      object-fit: contain;
    }

    .logo-text {
      .company-name {
        font-size: 18px;
        font-weight: 600;
        color: #2c5530;
        line-height: 1.2;
        margin-bottom: 4px;
      }

      .company-subtitle {
        font-size: 14px;
        color: #4a7c59;
        line-height: 1.2;
      }
    }
  }
}

// 登录容器样式
.login-container {
  position: absolute;
  right: 80px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.login-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 选项卡样式
.login-tabs {
  display: flex;
  margin-bottom: 32px;
  border-bottom: 1px solid #e5e5e5;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    font-size: 16px;
    color: #999;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;

    &.active {
      color: #2c5530;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 2px;
        background: #2c5530;
        border-radius: 1px;
      }
    }

    &:hover:not(.active) {
      color: #666;
    }
  }
}

// 表单样式
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    &.remember-me-item {
      margin-bottom: 32px;

      :deep(.el-checkbox) {
        .el-checkbox__label {
          color: #666;
          font-size: 14px;
        }

        .el-checkbox__input.is-checked .el-checkbox__inner {
          background-color: #2c5530;
          border-color: #2c5530;
        }
      }
    }

    &.login-button-item {
      margin-bottom: 0;
    }
  }
}

// 自定义输入框样式
.custom-input {
  :deep(.el-input__wrapper) {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: none;
    padding: 0 16px;
    height: 48px;

    &:hover {
      border-color: #2c5530;
    }

    &.is-focus {
      border-color: #2c5530;
      box-shadow: 0 0 0 2px rgba(44, 85, 48, 0.1);
    }

    .el-input__inner {
      color: #333;
      font-size: 14px;

      &::placeholder {
        color: #adb5bd;
      }
    }
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;

  .captcha-input {
    flex: 1;
  }

  .captcha-image {
    .login-code-img {
      height: 48px;
      border-radius: 8px;
      cursor: pointer;
      border: 1px solid #e9ecef;
      transition: border-color 0.3s ease;

      &:hover {
        border-color: #2c5530;
      }
    }
  }
}

// 登录按钮样式
.login-button {
  width: 100%;
  height: 48px;
  background: #2c5530;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background: #1e3a21;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-loading {
    background: #2c5530;
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .login-container {
    right: 40px;
  }
}

@media (max-width: 768px) {
  .logo-container {
    top: 20px;
    left: 20px;

    .logo-content {
      padding: 12px 16px;

      .logo-img {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }

      .logo-text {
        .company-name {
          font-size: 14px;
        }

        .company-subtitle {
          font-size: 12px;
        }
      }
    }
  }

  .login-container {
    right: 20px;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);

    .login-form-wrapper {
      width: 100%;
      padding: 32px 24px;
    }
  }
}

@media (max-width: 480px) {
  .logo-container {
    position: relative;
    top: 20px;
    left: 0;
    display: flex;
    justify-content: center;

    .logo-content {
      .logo-text {
        .company-name {
          font-size: 13px;
        }

        .company-subtitle {
          font-size: 11px;
        }
      }
    }
  }

  .login-container {
    position: relative;
    top: 40px;
    right: auto;
    left: auto;
    transform: none;
    padding: 0 20px;

    .login-form-wrapper {
      padding: 24px 20px;
    }
  }
}
</style>

<template>
    <div>
        <el-dialog v-model="isShowResult" title="详情" width="1000" @close="close" append-to-body>
            <div class="title">小麦农业服务套餐包（2025年小麦季）</div>
            <el-form :model="formModel" ref="formRef">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="作物" prop="crop">
                            <el-input v-model="formModel.crop" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="服务区域" prop="serverArea"
                            :rules="[{ required: true, message: '请输入服务面积', trigger: 'blur' }]">
                            <el-input v-model="formModel.serverArea" >
                                <template #append>亩</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="flatTableData" border style="width: 100%" :span-method="tableSpanMethod"
                    @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" ref="tableRef">
                    <el-table-column label="服务阶段" width="100">
                        <template #default="scope">
                            <span v-if="scope.row.stage">{{ scope.row.stage }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column type="selection" width="50" :selectable="selectable" />
                    <el-table-column prop="id" label="序号" width="60" align="center" />
                    <el-table-column prop="content" label="服务内容" min-width="120" />
                    <el-table-column prop="price" label="市场价（元/亩）" min-width="120" align="center">
                        <template #default="scope">
                            <!-- :rules="[{ required: true, message: '请输入金额', trigger: 'blur' }]" -->
                            <el-form-item
                                :prop="`tableData.${scope.row._groupIndex}.children.${scope.row._rowIndex}.price`"
                                style="margin-bottom:0" :validate-on-rule-change="false"
                                :key="`content-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                                <el-input
                                    v-model="formModel.tableData[scope.row._groupIndex].children[scope.row._rowIndex].price"
                                    placeholder="请输入金额"
                                    @blur="() => handlePriceChange(scope.row._groupIndex, scope.row._rowIndex)" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column prop="standard" label="作业标准" min-width="180" />
                </el-table>
                <div class="money-box">
                    <span>合计：</span>
                    <span>市场价金额：{{formModel.marketAmount }}</span>
                    <div class="discount-box">
                        <span>折扣金额：</span>
                        <div>
                            <el-form-item label="" prop="crop">
                                <el-input v-model="formModel.discountAmount" placeholder="请输入折扣金额">
                                <template #append>元</template>
                                </el-input>
                            </el-form-item>
                            <div class="tip">
                                提示：可用额度100元
                            </div>

                        </div>
                    </div>
                    <span>合同金额：{{ formModel.contractAmount}}</span>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="viewMenu">查看套餐</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        
        <!-- 引入查看套餐组件 -->
        <view-menu ref="viewMenuRef"></view-menu>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch, defineExpose } from 'vue'
import ViewMenu from './viewMenu.vue'

const isShowResult = ref(true)
const formRef = ref()
const tableRef = ref(null)
const viewMenuRef = ref(null)
const selectedRows = ref([]);
const formModel = ref({
    crop: '',
    serverArea: '',
    marketAmount: 0,
    discountAmount: 0,
    contractAmount: 0,
    tableData: [
        {
            stage: '第一阶段',
            children: [
                { id: 1, content: '国审优质小麦种', price: 1392, standard: '周麦系列/郑麦系列/新麦系列等', selected: true },
                { id: 2, content: '肥料100斤', price: 5890, standard: '知名厂家定制专用肥', selected: true },
                { id: 3, content: '种衣剂', price: '', standard: '知名厂家定制', selected: false },
                { id: 4, content: '秸秆粉碎2遍', price: '', standard: '10公分粉碎程度标准化作业', selected: false },
                { id: 5, content: '秸秆粉碎5遍', price: '', standard: '50公分粉碎程度标准化作业', selected: false },
            ]
        },
        {
            stage: '第二阶段',
            children: [
                { id: 8, content: '播种', price: 500, standard: '驱动联农合一体播种机', selected: true },
                { id: 9, content: '除草剂', price: 700, standard: '知名厂家定制', selected: true },
                { id: 10, content: '一喷三防套餐3遍', price: 500, standard: '杀虫+杀菌+叶面肥+调节剂', selected: true },
                { id: 14, content: '浇水灌溉2次', price: 500, standard: '专业化自动喷灌设备', selected: false },
                { id: 15, content: '收割+短茬运输', price: 700, standard: '大型联合收割机', selected: false },
            ]
        }
    ]
})
const flatTableData = ref([])
defineExpose({isShowResult})
// 监听服务面积、选中行和价格的变化，重新计算金额
watch([() => formModel.value.serverArea, selectedRows, () => flatTableData.value], () => {
    calculateMarketAmount();
}, { deep: true });

// 监听折扣金额的变化，重新计算合同金额
watch(() => formModel.value.discountAmount, () => {
    calculateContractAmount();
});

// 计算市场价金额
function calculateMarketAmount() {
    console.log(selectedRows.value)
    let totalPrice = 0;
    // 获取选中行的ID
    const selectedIds = selectedRows.value.map(row => row.id);
    
    // 从原始表格数据中获取最新价格
    formModel.value.tableData.forEach(group => {
        group.children.forEach(item => {
            // 如果该项被选中
            if (selectedIds.includes(item.id)) {
                // 确保价格是数字
                const price = parseFloat(item.price) || 0;
                totalPrice += price;
            }
        });
    });
    // 计算市场价金额 = 价格总和 * 服务面积
    const area = parseFloat(formModel.value.serverArea) || 0;
    formModel.value.marketAmount = (totalPrice * area).toFixed(2);
    
    // 同时更新合同金额
    calculateContractAmount();
}

// 计算合同金额
function calculateContractAmount() {
    // 合同金额 = 市场价金额 - 折扣金额
    const marketAmount = parseFloat(formModel.value.marketAmount) || 0;
    const discountAmount = parseFloat(formModel.value.discountAmount) || 0;
    formModel.value.contractAmount = (marketAmount - discountAmount).toFixed(2);
}

onMounted(() => {
    const arr = []
    formModel.value.tableData.forEach((group, groupIdx) => {
        group.children.forEach((item, rowIdx) => {
            arr.push({
                ...item,
                stage: rowIdx === 0 ? group.stage : '',
                _groupIndex: groupIdx,
                _rowIndex: rowIdx,
                _isFirstInGroup: rowIdx === 0,
                _groupCount: group.children.length, 
            })
        })
    })
    flatTableData.value = arr
    setDefaultSelection()
})

function setDefaultSelection() {
    nextTick(() => {
        // 确保tableRef.value已初始化
        if (tableRef.value) {
            // 先清除所有选择，避免重复勾选
            tableRef.value.clearSelection();
            
            // 遍历所有行，勾选selected为true的行
            flatTableData.value.forEach(row => {
                if (row.selected) {
                    tableRef.value.toggleRowSelection(row, true);
                }
            });
            
            // 初始化时同步选中状态到selectedRows
            const selectedItems = flatTableData.value.filter(item => item.selected);
            selectedRows.value = selectedItems;
            
            // 初始计算市场价金额
            calculateMarketAmount();
        }
    });
}

// 合并"服务阶段"单元格
function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
    if (columnIndex === 0) {
        if (row.stage) {
            return {
                rowspan: row._groupCount,
                colspan: 1
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0
            };
        }
    }
}

function selectable(row) {
    return true;
}

function tableRowClassName({ row }) {
    return '';
}

function handleSelectionChange(val) {
    selectedRows.value = val;
    // 选择变化时重新计算市场价金额
    calculateMarketAmount();
}

function handlePriceChange(groupIdx, rowIdx) {
    if (formRef.value) {
        formRef.value.clearValidate(`tableData.${groupIdx}.children.${rowIdx}.price`);
        formRef.value.validateField(`tableData.${groupIdx}.children.${rowIdx}.price`);
    }
    // 价格变化时重新计算市场价金额
    calculateMarketAmount();
}

// 查看套餐
function viewMenu() {
    if (viewMenuRef.value) {
        // 准备要传递的数据
        const menuData = {
            serverArea: formModel.value.serverArea,
            marketAmount: formModel.value.marketAmount,
            tableData: JSON.parse(JSON.stringify(formModel.value.tableData)) // 深拷贝避免引用问题
        };
        
        // 调用viewMenu组件的setMenuData方法传递数据
        viewMenuRef.value.setMenuData(menuData);
    }
}

function resetData(){
    selectedRows.value=[]
    formModel.value.tableData=[]
    flatTableData.value=[]
    formModel.value.marketAmount=0
    formModel.value.discountAmount=0
    formModel.value.contractAmount=0
    formModel.value.crop=''
    formModel.value.serverArea=''
}
function close() {
    isShowResult.value = false
}

function submitForm() {
    if (formRef.value) {
        formRef.value.validate(valid => {
            if (valid) {
                // 根据选中状态更新selected属性
                const ids = selectedRows.value.map(item => item.id)
                flatTableData.value.forEach(item => {
                    if (ids.includes(item.id)) {
                        item.selected = true
                    } else {
                        item.selected = false
                    }
                })
                formModel.value.tableData.forEach(item => {
                    item.children.forEach(child => {
                        if (ids.includes(child.id)) {
                            child.selected = true
                        } else {
                            child.selected = false
                        }
                    })
                })
                
                // 生成新的newTableData，只包含selected为true的项
                let newTableData = []
                formModel.value.tableData.forEach((group, groupIdx) => {
                    // 筛选出选中的子项
                    const selectedChildren = group.children.filter(child => child.selected);
                    
                    // 如果该分组有选中的子项，则创建新的分组
                    if (selectedChildren.length > 0) {
                        // 创建新的分组对象
                        const newGroup = {
                            stage: group.stage,
                            children: selectedChildren
                        };
                        
                        // 将新分组添加到结果数组
                        newTableData.push(newGroup);
                    }
                });
                
                // 生成扁平化的结果数据，格式与flatTableData一致
                let flatNewTableData = []
                newTableData.forEach((group, groupIdx) => {
                    group.children.forEach((item, rowIdx) => {
                        flatNewTableData.push({
                            ...item,
                            stage: rowIdx === 0 ? group.stage : '',
                            _groupIndex: groupIdx,
                            _rowIndex: rowIdx,
                            _isFirstInGroup: rowIdx === 0,
                            _groupCount: group.children.length
                        })
                    })
                })
                
                console.log('表单数据', formModel.value);
                console.log('选中行', selectedRows.value);
                console.log('新生成的数据结构', newTableData);
                console.log('新生成的扁平数据', flatNewTableData);
                // 这里可以添加提交逻辑
            }
        });
    }
}

function cancel() {
    close();
}
</script>

<style scoped lang="scss">
.title {
    width: 100%;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}
.money-box{
    display: flex; 
    margin-top: 10px;
    &>span{
       margin-right: 30px;
    }
    .discount-box{
        display: flex;
        margin-right: 30px;
        .tip{
            position: relative;
            top:-10px;
        }
    }
}
</style>
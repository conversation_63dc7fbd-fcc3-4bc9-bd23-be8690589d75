<template>
    <div>
        <el-dialog v-model="isShowDialog" :title="title" width="1000" @close="close" append-to-body>
            <el-form :model="formModel" ref="formRef">
                <el-form-item label="套餐名称" prop="name"
                    :rules="[{ required: true, message: '请输入套餐名称', trigger: 'blur' }]">
                    <el-input style="width: 300px;" v-model="formModel.name" placeholder="请输入套餐名称" />
                </el-form-item>
                <el-form-item label="套餐编号" prop="code"
                    :rules="[{ required: true, message: '请输入套餐编号', trigger: 'blur' }]">
                    <el-input style="width: 300px;" v-model="formModel.code" placeholder="请输入套餐编号" />
                </el-form-item>
                <el-form-item label="作物类型" prop="cropType"
                    :rules="[{ required: true, message: '请选择作物类型', trigger: 'blur' }]">
                    <el-select style="width: 300px;" v-model="formModel.cropType" placeholder="请选择作物类型">
                        <el-option label="小麦" value="小麦" />
                        <el-option label="玉米" value="玉米" />
                        <el-option label="水稻" value="水稻" />
                    </el-select>
                </el-form-item>
                <el-table :data="flatTableData" border style="width: 100%" :span-method="tableSpanMethod">
                    <!-- 阶段操作列 -->
                    <el-table-column label="" >
                        <template #default="scope">
                            <div v-if="scope.row._isFirstInGroup"
                               >
                                <el-button v-if="formModel.tableData.length > 1" type="text" style="color:#f88"
                                    @click="removeStage(scope.row._groupIndex)">删除阶段</el-button>
                                <el-button type="text" @click="addStage(scope.row._groupIndex)">新增阶段</el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 阶段名称 -->
                    <el-table-column label="服务阶段" width="100">
                        <template #default="scope"> 
                            <span v-if="scope.row._isFirstInGroup">{{ scope.row.stage }}</span>
                        </template>
                    </el-table-column>
                    <!-- 类型 必填 -->
                    <el-table-column label="类型*" width="200">
                        <template #header>
                            <span>类型</span>
                            <span style="color:red;margin-left: 5px;">*</span>
                        </template>
                        <template #default="scope"> 
                            <el-form-item
                                :prop="`tableData.${scope.row._groupIndex}.children.${scope.row._rowIndex}.type`"
                                :rules="[{ required: true, message: '请选择类型', trigger: 'change' }]"
                                style="margin-bottom:0" :validate-on-rule-change="false"
                                :key="`type-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                                <el-select
                                    v-model="formModel.tableData[scope.row._groupIndex].children[scope.row._rowIndex].type"
                                    placeholder="请选择类型" 
                                    @change="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'type')">
                                    <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item" />
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <!-- 服务内容 必填 -->
                    <el-table-column label="服务内容*" width="200">
                        <template #header>
                            <span>服务内容</span>
                            <span style="color:red;margin-left: 5px;">*</span>
                        </template>
                        <template #default="scope">
                            <el-form-item
                                :prop="`tableData.${scope.row._groupIndex}.children.${scope.row._rowIndex}.content`"
                                :rules="[{ required: true, message: '请填写服务内容', trigger: 'blur' }]"
                                style="margin-bottom:0" :validate-on-rule-change="false"
                                :key="`content-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                                <el-input
                                    v-model="formModel.tableData[scope.row._groupIndex].children[scope.row._rowIndex].content"
                                    placeholder="请填写服务内容"
                                    @blur="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'content')" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <!-- 作业标准 必填 -->
                    <el-table-column label="作业标准*" width="200">
                        <template #header>
                            <span>作业标准</span>
                            <span style="color:red;margin-left: 5px;">*</span>
                        </template>
                        <template #default="scope">
                            <el-form-item
                                :prop="`tableData.${scope.row._groupIndex}.children.${scope.row._rowIndex}.standard`"
                                :rules="[{ required: true, message: '请填写作业标准', trigger: 'blur' }]"
                                style="margin-bottom:0" :validate-on-rule-change="false"
                                :key="`standard-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                                <el-input
                                    v-model="formModel.tableData[scope.row._groupIndex].children[scope.row._rowIndex].standard"
                                    placeholder="请填写作业标准"
                                    @blur="() => handleFieldChange(scope.row._groupIndex, scope.row._rowIndex, 'standard')" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <!-- 操作 -->
                    <el-table-column label="操作" width="80">
                        <template #default="scope">
                            <el-icon color="#409EFF" size="20"
                            style="cursor: pointer;"
                                @click="addRow(scope.row._groupIndex, scope.row._rowIndex)">
                                <CirclePlusFilled />
                            </el-icon>
                            <el-icon color="#F56C6C" size="20"
                             style="cursor: pointer;"
                                v-if="formModel.tableData[scope.row._groupIndex].children.length > 1"
                                @click="removeRow(scope.row._groupIndex, scope.row._rowIndex)">
                                <RemoveFilled />
                            </el-icon>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                  <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, defineExpose } from 'vue'
import { convertToChineseNumber } from '@/utils/index'
const isShowDialog = ref(true)
const title = ref('新增套餐')
const formRef = ref(null)
const formModel = ref({
    name: '',
    code: '',
    cropType: '',
    tableData: [
    {
      stage: '第一阶段',
      children: [
        { type: '', content: '', standard: '' },
        { type: '', content: '', standard: '' }
      ]
    },
    {
      stage: '第二阶段',
      children: [
        { type: '', content: '', standard: '' }
      ]
    }
  ]
})
const typeOptions = ['投入品', '农机作业', '无人机作业']
function reorderStages() {
  formModel.value.tableData.forEach((item, idx) => {
    item.stage = `第${convertToChineseNumber(idx+1)}阶段`
  })
}

const flatTableData = computed(() => {
  const arr = []
  formModel.value.tableData.forEach((group, groupIdx) => {
    group.children.forEach((item, rowIdx) => {
      arr.push({
        ...item,
        stage: rowIdx === 0 ? group.stage : '',
        _groupIndex: groupIdx,
        _rowIndex: rowIdx,
        _isFirstInGroup: rowIdx === 0,
        _groupCount: group.children.length
      })
    })
  })
  return arr
})

function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 操作列和服务阶段列
  if (columnIndex === 0 || columnIndex === 1) {
    if (row._isFirstInGroup) {
      return { rowspan: row._groupCount, colspan: 1 }
    } else {
      return { rowspan: 0, colspan: 0 }
    }
  }
}

function removeStage(groupIdx) {
  if (formModel.value.tableData.length === 1) {
    ElMessage.warning('至少保留一个阶段')
    return
  }
  formModel.value.tableData.splice(groupIdx, 1)
  reorderStages()
}

function addStage(groupIdx) {
  formModel.value.tableData.splice(groupIdx + 1, 0, {
    stage: '',
    children: [
      { type: '', content: '', standard: '' }
    ]
  })
  reorderStages()
}

function removeRow(groupIdx, rowIdx) {
  const group = formModel.value.tableData[groupIdx]
  if (group.children.length === 1) {
    ElMessage.warning('每个阶段至少保留一行')
    return
  }
  group.children.splice(rowIdx, 1)
}

function addRow(groupIdx, rowIdx) {
  formModel.value.tableData[groupIdx].children.splice(rowIdx, 0, { type: '', content: '', standard: '' })
}
function close() {
    isShowDialog.value = false
}

function handleFieldChange(groupIdx, rowIdx, field) {
  if (formRef.value) {
    formRef.value.clearValidate(`tableData.${groupIdx}.children.${rowIdx}.${field}`)
    formRef.value.validateField(`tableData.${groupIdx}.children.${rowIdx}.${field}`)
  }
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      ElMessageBox.alert(
        `<pre>${JSON.stringify(formModel.value.tableData, null, 2)}</pre>`,
        '提交数据',
        { dangerouslyUseHTMLString: true }
      )
    } else {
      ElMessage.error('请完善所有必填项！')
    }
  })
}
defineExpose({ isShowDialog })

</script>
<style scoped lang="scss"></style>
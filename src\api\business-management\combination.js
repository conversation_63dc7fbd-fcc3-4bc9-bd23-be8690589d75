import request from '@/utils/request'

// 查询套餐列表
export function listCombination(query) {
  return request({
    url: '/combination/list',
    method: 'get',
    params: query
  })
}

// 查询套餐详细
export function getCombination(id) {
  return request({
    url: '/combination/detail/' + id,
    method: 'get'
  })
}

// 新增套餐
export function addCombination(data) {
  return request({
    url: '/combination/add',
    method: 'post',
    data: data
  })
}

// 删除套餐
export function delCombination(id) {
  return request({
    url: '/combination/delete/' + id,
    method: 'post'
  })
}

// 停用套餐
export function disActivate(id) {
  return request({
    url: '/combination/disActivate/' + id,
    method: 'post'
  })
}

// 启用套餐
export function activate(id) {
  return request({
    url: '/combination/activate/' + id,
    method: 'post'
  })
} 
export function newNumber(params) {
  return request({
    url: '/combination/newNumber',
    method: 'get',
    params,
  })
}
export function orderDetail(params) {
  return request({
    url: '/order/detail',
    method: 'get',
    params,
  })
}


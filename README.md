# zkss-vue

```
cd existing_repo
git remote add origin http://*************:8001/farm/zkss-vue.git
git branch -M main
git push -uf origin main
```

## 前端运行

```bash
# 克隆项目
git clone http://*************:8001/farm/zkss-vue.git

# 进入项目目录
cd zkss-vue

# 安装依赖
yarn --registry=https://registry.npmmirror.com

# 启动服务
yarn dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```
<template>
  <el-select
    v-model="model"
    filterable
    remote
    :remote-method="handleSearch"
    :loading="loading"
    :placeholder="placeholder"
    @visible-change="handleVisibleChange"
    style="width: 100%"
    :clearable="clearable"
    ref="selectRef"
  >
    <el-option
      v-for="item in options"
      :key="item[valueKey]"
      :label="item[labelKey]"
      :value="item[valueKey]"
    />
    <div v-if="loadingMore" class="el-select-dropdown__loading">加载中...</div>
    <div v-if="noMore && options.length" class="el-select-dropdown__empty">没有更多了</div>
    <div v-if="!options.length && !loading" class="el-select-dropdown__empty">暂无数据</div>
  </el-select>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, nextTick, onBeforeUnmount } from 'vue'

const props = defineProps({
  modelValue: [String, Number, Object, Array],
  placeholder: { type: String, default: '请选择' },
  labelKey: { type: String, default: 'label' },
  valueKey: { type: String, default: 'value' },
  pageSize: { type: Number, default: 20 },
  clearable: { type: Boolean, default: true }
})
const emit = defineEmits(['update:modelValue', 'change'])

const model = ref(props.modelValue)
const options = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const noMore = ref(false)
const page = ref(1)
const total = ref(0)
const searchQuery = ref('')
const selectRef = ref(null)
let dropdownEl = null

watch(() => props.modelValue, val => { model.value = val })
watch(model, val => { emit('update:modelValue', val); emit('change', val) })

// mock数据源
const mockData = Array.from({ length: 137 }, (_, i) => ({
  value: i + 1,
  label: `选项${i + 1}`
}))

// 模拟接口
const fetchMockList = async (query, pageNum, pageSize) => {
  let filtered = mockData
  if (query) {
    filtered = filtered.filter(item => item.label.includes(query))
  }
  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  return {
    list: filtered.slice(start, end),
    total: filtered.length
  }
}

// 远程搜索
const handleSearch = (query) => {
  searchQuery.value = query
  page.value = 1
  noMore.value = false
  fetchOptions(query, 1)
}

// 下拉出现时，首次加载并绑定scroll
const handleVisibleChange = async (visible) => {
  if (visible) {
    if (!options.value.length) handleSearch('')
    await nextTick()
    setTimeout(() => {
      bindDropdownScroll()
    }, 50)
  } else {
    unbindDropdownScroll()
  }
}

// 绑定scroll事件
function bindDropdownScroll() {
  unbindDropdownScroll()
  // el-select下拉面板的class为.el-scrollbar__wrap
  const popper = document.querySelector('.el-select-dropdown .el-scrollbar__wrap')
  if (popper) {
    dropdownEl = popper
    dropdownEl.addEventListener('scroll', onDropdownScroll)
  }
}

// 解绑scroll事件
function unbindDropdownScroll() {
  if (dropdownEl) {
    dropdownEl.removeEventListener('scroll', onDropdownScroll)
    dropdownEl = null
  }
}

function onDropdownScroll(e) {
  const el = e.target
  if (
    el.scrollTop + el.clientHeight >= el.scrollHeight - 10 &&
    !loadingMore.value &&
    !noMore.value
  ) {
    loadMore()
  }
}

onBeforeUnmount(() => {
  unbindDropdownScroll()
})

const fetchOptions = async (query, pageNum) => {
  loading.value = pageNum === 1
  loadingMore.value = pageNum > 1
  try {
    const { list, total: totalCount } = await fetchMockList(query, pageNum, props.pageSize)
    if (pageNum === 1) {
      options.value = list
    } else {
      options.value = options.value.concat(list)
    }
    total.value = totalCount
    noMore.value = options.value.length >= totalCount
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMore = () => {
  if (noMore.value) return
  page.value += 1
  fetchOptions(searchQuery.value, page.value)
}
</script>

<style scoped>
.el-select-dropdown__loading,
.el-select-dropdown__empty {
  text-align: center;
  color: #999;
  padding: 8px 0;
  font-size: 13px;
}
</style>
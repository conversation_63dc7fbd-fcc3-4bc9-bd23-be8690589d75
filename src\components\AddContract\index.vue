<template>
  <div>
    <el-dialog
      v-model="isShowAddDialog"
      title="上传合同"
      width="1000"
      @close="close"
      append-to-body
    >
      <el-form :model="contractForm" ref="contractFormRef" :rules="contractRules">
        <el-form-item label="签署日期" prop="signDate">
          <el-date-picker  
            v-model="contractForm.signDate"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="请选择签署日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="所属种植户" v-if="isShowfarmer" prop="farmerId">
            <el-select filterable v-model="contractForm.farmerId" placeholder="请选择所属种植户">
                <el-option v-for="item in farmerList" :key="item.customerId" :label="item.label" :value="item.customerId"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="所属业务员" prop="employeeId">
            <el-select filterable v-model="contractForm.employeeId" placeholder="请选择所属业务员" @change="handleEmployeeChange">
                <el-option v-for="item in employeeList" :key="item.employeeId" :label="item.label" :value="item.employeeId"></el-option>
            </el-select>
        </el-form-item>
            <el-form-item label="所属村集体" prop="villageId">
            <el-select filterable v-model="contractForm.villageId" placeholder="请选择所属村集体">
                <el-option v-for="item in villageList" :key="item.customerId" :label="item.label" :value="item.customerId"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="合同附件" prop="orderAttachments">
          <!-- <image-upload v-model="contractForm.orderAttachments" :fileSize="10" :limit="20" placeholder="请上传合同附件" /> -->
          <FileUpload btnName="上传合同附件" v-model="contractForm.orderAttachments" :fileType="['jpg','jpeg','png','doc','docx','pdf']" :fileSize="10" :limit="20" placeholder="请上传合同附件" />
        </el-form-item>
        <el-form-item label="识别附件" prop="attachmentPathList">
          <uploadFile  listType='' @uploadSuccess="uploadSuccess" v-model="contractForm.attachmentPathList" :fileType="['jpg','jpeg','png','.pdf']" action="/order/orderRecognition" :fileSize="10" :limit="1" placeholder="请上传识别" />
        </el-form-item>
        <div v-if="flatNewserviceStageList.length>0" style="display: flex;align-items: center;justify-content: space-between;margin-bottom: 10px;" >
            <span>{{ contractForm.combinationName }}</span>
            <span>订单作物：{{ jobGroupFormat(contractForm.plantTypeId) }}</span>
            <span>服务面积：{{ contractForm.serviceArea }}</span>
            <span style="color: #409EFF;cursor: pointer;" @click="handleEdit">修改</span>
        </div>
        <el-table v-if="flatNewserviceStageList.length>0" :data="flatNewserviceStageList" border style="width: 100%" :span-method="tableSpanMethod1"
                    @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" ref="tableRef">
                    <el-table-column label="服务阶段" width="100">
                        <template #default="scope">
                            <span>第{{ convertToChineseNumber(scope.row._groupIndex + 1) }}阶段</span>
                        </template>
                    </el-table-column>
                
                    <el-table-column label="序号" width="60" align="center">
                      <template #default="scope">
                        {{ scope.$index + 1 }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="serviceContent" label="服务内容" min-width="120" />
                    <el-table-column prop="marketPrice" label="市场价（元/亩）" min-width="120" align="center">
                     
                    </el-table-column>
                    <el-table-column prop="standard" label="作业标准" min-width="180" />
                </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <!-- <el-button @click="viewMenu">查看套餐</el-button> -->
        </div>
      </template>
    </el-dialog>
    <ocrContract @confirm="confirmOcr" :availableAmountPercent="availableAmountPercent" :lastAvailableAmount="contractForm.lastAvailableAmount" ref="ocrContractRef" />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch, defineExpose,defineEmits,defineProps } from "vue";
import { getAllGrowerAndVillage,employeeDropdown,orderRecognition,orderCreate } from "@/api/customer/contract";
import { convertToChineseNumber,getDate } from '@/utils/index'
import useUserStore from '@/store/modules/user'
import uploadFile from '@/components/ImageUpload/uploadFile.vue'
import ocrContract from './ocrContract.vue'
const props=defineProps({
  isShowfarmer:{
    type:Boolean,
    default:true
  }
})
const emit = defineEmits(['addContractSuccess'])
const { proxy } = getCurrentInstance()
const { plant_type } = proxy.useDict("plant_type")
const userStore = useUserStore()
const isShowAddDialog = ref(false);
const contractFormRef = ref(null);
const contractForm = ref({
    deptId:userStore.deptId,
    signDate: getDate(),
    farmerId:'',
    employeeId:'',
    villageId:'',
    orderAttachments:[],
    attachmentPathList:[],
    attachmentPath:[],
    newAvailableAmount:'',
    lastAvailableAmount:'',
});
const availableAmountPercent=ref(0) //折扣百分比
const contractRules = ref({
  signDate: [{ required: true, message: "请选择签署日期", trigger: "blur" }],
  farmerId: [{ required: true, message: "请选择所属种植户", trigger: "blur" }],
  employeeId: [{ required: true, message: "请选择所属业务员", trigger: "blur" }],
  // villageId: [{ required: true, message: "请选择所属村集体", trigger: "blur" }],
  orderAttachments: [{ required: true, message: "请上传合同照片", trigger: "blur" }],
  attachmentPathList: [{ required: true, message: "请上传附件照片", trigger: "blur" }],
});
const farmerList = ref([]);
const employeeList = ref([]);
const villageList = ref([]);     
const ocrContractRef=ref(null)
const flatNewserviceStageList=ref([])
onMounted(()=>{
    getAllGrowerAndVillage().then(res=>{ 
        if(res.data&&res.data.growerList.length){
            res.data.growerList.forEach(item=>{
                item.label=`${item.customerName}(${item.contactPerson}-${item.phoneNumber})`
            })
        }
        if(res.data&&res.data.villageList.length){
            res.data.villageList.forEach(item=>{
                item.label=`${item.customerName}(${item.contactPerson}-${item.phoneNumber})`
            })
        }
        farmerList.value=res.data.growerList || []; 
        villageList.value=res.data.villageList || []; 
    }) 
    employeeDropdown({deptId:userStore.deptId,jobType:0}).then(res=>{
        if(res.data&&res.data.length>0){
            res.data.forEach(item=>{
           item.label=`${item.employeeName}(${item.phoneNumber})`
            })
        }
        employeeList.value=res.data || []
    })
})
function handleEmployeeChange(val){
  const info=employeeList.value.find(item=>item.employeeId==val) 
  if(info){
    contractForm.value.lastAvailableAmount=info.availableAmount
    availableAmountPercent.value=info.discountRate
  }
}
/** 任务组名字典翻译 */
function jobGroupFormat(val) {
  return proxy.selectDictLabel(plant_type.value, val)
}
function uploadSuccess(info){
    console.log(10666,contractForm.value.attachmentPath) 
    contractForm.value.combinationId=info.id
    contractForm.value.combinationName=info.name 
    if(info.id){
      delete info.id
      delete info.name
    }
    info.combinationName=contractForm.value.combinationName
    contractForm.value.attachmentPath=contractForm.value.attachmentPathList[0].relativeUrl
    ocrContractRef.value.isShowResult=true 
    ocrContractRef.value.formModel=info 
    ocrContractRef.value.getFlatserviceStageList()
}
function handleEdit(){
  ocrContractRef.value.isShowResult=true 
}
// 合并"服务阶段"单元格
function tableSpanMethod1({ row, column, rowIndex, columnIndex }) {
  // 只合并第一列（服务阶段）
  if (columnIndex === 0) {
    if (row._rowIndex === 0) {
      return {
        rowspan: row._groupCount,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
}
function confirmOcr(info){
  console.log(16555,info.allForm) 
  if(!info.allForm.signDate){
    delete info.allForm.signDate
  }
    flatNewserviceStageList.value=info.flatNewserviceStageList
    contractForm.value=Object.assign(contractForm.value,JSON.parse(JSON.stringify(info.allForm))) 
    contractForm.value.newAvailableAmount=info.newAvailableAmount
}
function submitForm(){
    console.log(11555,contractForm.value)
    contractFormRef.value.validate((valid)=>{
        // 把serviceStageList名称换成orderPhaseReqList，里面的serviceStageList名称换成orderPhaseDetails
        if( contractForm.value.serviceStageList){ 
          contractForm.value.orderPhaseReqList=contractForm.value.serviceStageList
        delete contractForm.value.serviceStageList
        contractForm.value.orderPhaseReqList.forEach(item=>{
            item.orderPhaseDetails=item.serviceStageItemResList
            delete item.serviceStageItemResList
        })
        }
        if(contractForm.value.serviceStageItemList){
          delete contractForm.value.serviceStageItemList
        }
        contractForm.value.orderPhaseReqList.forEach((item,index)=>{
          item.combinationPhaseId=item.id
          // item.servicePhase=item.index
          item.orderPhaseDetails.forEach(child=>{
            child.combinationPhaseDetailId=child.id
          })
        })
        if(valid){ 
            orderCreate(contractForm.value).then(res=>{
              if(res.code==200){
                isShowAddDialog.value=false
                proxy.$modal.msgSuccess("上传成功")
                emit('addContractSuccess')
                resetForm()
              }
            })
        }
    })
}
function close(){
    resetForm()
    isShowAddDialog.value=false
}
function resetForm(){
    contractFormRef.value.resetFields()
    flatNewserviceStageList.value=[]
    const keys=Object.keys(contractForm.value)
    keys.forEach(key=>{
        contractForm.value[key]=''
    })  
}
defineExpose({ isShowAddDialog ,contractForm});
</script>

<style scoped lang="scss">
</style>
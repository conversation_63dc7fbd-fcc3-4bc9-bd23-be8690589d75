<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch">
      <el-form-item label="关键字" prop="nameOrNumber">
        <el-input v-model="queryParams.nameOrNumber" placeholder="客户名称/联系人/联系电话" clearable @keyup.enter="handleQuery"
          maxlength="64" />
      </el-form-item>
      <el-form-item label="客户类型" prop="customerType">
        <el-select v-model="queryParams.customerType" placeholder="请选择客户类型" clearable style="width: 180px">
          <el-option v-for="dict in zkss_customer_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="reviewStatus">
        <el-select v-model="queryParams.reviewStatus" placeholder="请选择审核状态" clearable style="width: 180px">
          <el-option v-for="dict in zkss_review" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['customerManagement:customer:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['customerManagement:customer:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['customerManagement:customer:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['customerManagement:employee:importData']">导入</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['customerManagement:customer:export']">导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="customerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column label="客户名称" align="center" prop="customerName" />
      <el-table-column label="客户类型" align="center" prop="customerType">
        <template #default="scope">
          <dict-tag :options="zkss_customer_type" :value="scope.row.customerType" />
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="phoneNumber" />
      <el-table-column label="详细地址" align="center" prop="address">
        <template #default="scope">
          {{ scope.row.region }}{{ scope.row.address }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="reviewStatus">
        <template #default="scope">
          <span v-if="scope.row.reviewStatus === '0' || scope.row.reviewStatus === 0"
            class="zkss-status-pending">待审核</span>
          <span v-else-if="scope.row.reviewStatus === '1' || scope.row.reviewStatus === 1"
            class="zkss-status-done">已审核</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)"
            v-hasPermi="['customerManagement:customer:edit']">修改</el-button>
          <el-button type="text" @click="handleDelete(scope.row)"
            v-hasPermi="['customerManagement:customer:remove']">删除</el-button>
          <el-button v-hasPermi="['customerManagement:customer:details']" type="text" @click="handleView(scope.row)">查看详情</el-button>
          <el-button  v-hasPermi="['customerManagement:customer:contract']" v-if="scope.row.customerType === '3'" type="text"
            @click="handleViewContract(scope.row)">查看合同</el-button>
          <el-button v-hasPermi="['customerManagement:customer:contract']" v-if="scope.row.customerType === '3' && scope.row.orderCount <= 0" type="text"
            @click="handleAddContract(scope.row)">上传合同</el-button>
          <el-button v-hasPermi="['customerManagement:customer:review']" v-if="scope.row.reviewStatus === '0'" type="text"
            @click="openReviewDialog(scope.row)">审核通过</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改客户对话框 -->
    <el-dialog :title="title" v-model="open" width="650px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="代理商" prop="deptId" v-hasPermi="['system:dept:select']">
          <el-tree-select v-model="form.deptId" :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择代理商"
            check-strictly clearable @change="handleDeptChange" />
        </el-form-item>
        <el-form-item label="客户类型" prop="customerType">
          <el-select v-model="form.customerType" placeholder="请选择客户类型">
            <el-option v-for="dict in zkss_customer_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="form.customerName" placeholder="请输入客户名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="联系电话" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入联系电话，将作为种植户的默认账号，请慎重填写。" maxlength="11" />
        </el-form-item>
        <el-form-item label="紧急联系人" prop="urgentContactPerson">
          <el-input v-model="form.urgentContactPerson" placeholder="请输入紧急联系人" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="紧急联系电话" prop="urgentPhoneNumber">
          <el-input v-model="form.urgentPhoneNumber" placeholder="请输入紧急联系电话" maxlength="11" />
        </el-form-item>
        <el-form-item label="所属区域" prop="region">
          <Cascader v-model="singleCode" placeholder="请选择区域" :maxLoadLevel="3" :maxSelectLevel="3"
            @change="handleAreaChangeSingle" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" maxlength="100" style="width: 80%;" />&nbsp;
          <el-input type="button" value="搜索" @click="onSearch" style="width: 10%;" />
          <div id="mapDiv" style="width: 100%;height: 400px;margin-top: 5px;"></div>
        </el-form-item>
        <el-form-item label="关联业务员" prop="employeeId">
          <el-select v-model="form.employeeId" placeholder="请选择业务员" filterable>
            <el-option v-for="item in employeeDropdownList" :key="item.employeeId" :label="item.employeeName"
              :value="item.employeeId" />
          </el-select>
        </el-form-item>
        <el-form-item label="营业执照" prop="businessLicenseList">
          <image-upload v-model="form.businessLicenseList" :limit="1" placeholder="请上传营业执照" />
        </el-form-item>
        <el-form-item label="户名" prop="householdName">
          <el-input v-model="form.householdName" placeholder="请输入户名" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="银行卡号" prop="cardBankNumber">
          <div style="display: flex; align-items: center;">
            <el-input v-model="form.cardBankNumber" placeholder="请输入银行卡号" maxlength="20" show-word-limit style="flex:1;" />
            <el-button type="primary" style="margin-left: 8px;" @click="handleRecognizeCard">识别</el-button>
          </div>
          <!-- 银行卡识别弹窗 -->
          <el-dialog v-model="cardOcrDialogVisible" title="银行卡识别" width="500px" append-to-body>
            <div style="text-align: center; margin-bottom: 20px;">
              <el-upload
                ref="cardOcrUploadRef"
                :auto-upload="false"
                :show-file-list="false"
                accept="image/*"
                :on-change="handleCardImageChange"
                drag
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">将银行卡图片拖到此处，或<em>点击上传</em></div>
                <template #tip>
                  <div class="el-upload__tip">只能上传jpg/png文件，且不超过10MB</div>
                </template>
              </el-upload>
            </div>
            <div v-if="cardOcrResult" style="margin-top: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;">
              <div style="font-weight: bold; margin-bottom: 10px;">识别结果：</div>
              <div style="margin-bottom: 8px;">
                <span style="font-weight: 500;">开户银行：</span>
                <span>{{ cardOcrResult.bankName || '未识别到' }}</span>
              </div>
              <div style="margin-bottom: 8px;">
                <span style="font-weight: 500;">银行卡号：</span>
                <span>{{ cardOcrResult.bankCardNumber || '未识别到' }}</span>
              </div>
            </div>
            <template #footer>
              <div class="dialog-footer">
                <el-button @click="cardOcrDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmCardOcr" :disabled="!cardOcrResult">确认使用</el-button>
              </div>
            </template>
          </el-dialog>
        </el-form-item>
        <el-form-item label="开户银行" prop="openingBank">
          <el-input v-model="form.openingBank" placeholder="请输入开户银行" maxlength="30" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 客户详情弹窗 -->
    <el-dialog :title="showContractTable ? '查看合同' : '查看客户'" v-model="detailOpen" width="1000px" append-to-body>
      <div style="background:#f7f7f7;padding:20px 20px 20px 20px;border-radius:8px;position:relative;">
        <!-- 右上角竖直绿色标签 -->
        <div style="position:absolute;right:0;top:20px;">
          <div
            style="background:#B7F5C2;color:#3CB371;padding:4px 12px;border-radius:8px 0 0 8px;writing-mode:vertical-rl;text-align:center;font-size:16px;">
            {{ getCustomerTypeLabel(detailData.customerType) }}
          </div>
        </div>
        <div style="display:flex;flex-direction:row;justify-content:space-between;flex-wrap:wrap;gap:0 24px;">
          <!-- 左列 -->
          <div style="flex:1;min-width:260px;max-width:33%;display:flex;flex-direction:column;gap:18px 0;">
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">客户名称</span>
              <span class="zkss-detail-value">{{ detailData.customerName }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">联系人</span>
              <span class="zkss-detail-value">{{ detailData.contactPerson }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">联系电话</span>
              <span class="zkss-detail-value">{{ detailData.phoneNumber }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">营业执照</span>
              <span class="zkss-detail-value">
                <!-- {{ detailData.businessLicenseList }} -->
                <img v-if="detailData.businessLicense" :src="detailData.businessLicenseList[0]?.url"
                  style="height:36px;vertical-align:middle;border-radius:4px;box-shadow:0 1px 4px #ccc;cursor:pointer;"
                  @click="handleBusinessLicensePreview(detailData.businessLicenseList[0]?.url)" />
              </span>
            </div>
          </div>
          <!-- 中列 -->
          <div style="flex:1;min-width:260px;max-width:33%;display:flex;flex-direction:column;gap:18px 0;">
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">紧急联系人</span>
              <span class="zkss-detail-value">{{ detailData.urgentContactPerson }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">紧急联系电话</span>
              <span class="zkss-detail-value">{{ detailData.urgentPhoneNumber }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">所属区域</span>
              <span class="zkss-detail-value">{{ detailData.regionCascadeName }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">详细地址</span>
              <span class="zkss-detail-value">{{ detailData.address }}</span>
            </div>
          </div>
          <!-- 右列 -->
          <div style="flex:1;min-width:260px;max-width:33%;display:flex;flex-direction:column;gap:18px 0;">
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">户名</span>
              <span class="zkss-detail-value">{{ detailData.householdName }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">银行卡号</span>
              <span class="zkss-detail-value">{{ detailData.cardBankNumber }}</span>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="zkss-detail-label">开户银行</span>
              <span class="zkss-detail-value">{{ detailData.openingBank }}</span>
            </div>
          </div>
        </div>
      </div>
      <div style="text-align: right;margin: 10px 0;">
        <el-button  type="primary" @click="addContract">新增</el-button>
      </div>
      <!-- 历史合同表格 - 只在查看合同时显示 -->
      <div v-if="showContractTable" style="margin-top: 20px;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
          <div style="width: 4px; height: 20px; background: var(--el-color-primary); margin-right: 8px;"></div>
          <span style="font-size: 16px; font-weight: bold;">历史合同</span>
        </div>

        <el-table :data="contractHistoryList" style="width: 100%">
          <el-table-column prop="signDate" label="签署日期" align="center" width="120" />
          <el-table-column prop="employeeName" label="所属业务员" align="center" width="120" />
          <el-table-column prop="customerName" label="归属村集体" align="center" width="150" />
          <el-table-column label="合同附件" align="center" width="100">
            <template #default="scope">
              <img v-if="scope.row.attachmentPath" :src="scope.row.attachmentPath"
                style="height:36px;cursor:pointer;border-radius:4px;box-shadow:0 1px 4px #ccc;"
                @click="handleBusinessLicensePreview(scope.row.attachmentPath)" />
              <el-icon v-else style="color: #dcdfe6;">
                <Document />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column prop="plantTypeId" label="订单作物" align="center" width="100" />
          <el-table-column prop="marketPrice" label="市场价金额(元)" align="center" width="140" />
          <el-table-column prop="discountPrice" label="折扣金额(元)" align="center" width="140" />
          <el-table-column prop="contractPrice" label="合同金额(元)" align="center" width="140" />

        </el-table>
      </div>
    </el-dialog>

    <!-- 营业执照图片预览弹窗 -->
    <el-dialog v-model="businessLicensePreviewVisible" :show-close="true" append-to-body title=""
      style="text-align:center">
      <img :src="businessLicensePreviewUrl"
        style="max-width:600px;width:100%;height:auto;border-radius:8px;box-shadow:0 2px 12px #888;" />
    </el-dialog>

    <!-- 审核确认弹窗 -->
    <el-dialog title="审核确认" v-model="reviewDialogOpen" width="550px" append-to-body>
      <div style="text-align:center;padding:24px 0 0 0;">
        <div style="font-size:18px;margin-bottom:16px;">
          你确定要<span style="color:#409EFF;font-weight:bold;">审核通过</span>吗？
          审核通过后将<span style="color:#F56C6C;font-weight:bold;">无法撤回</span>，请慎重操作~
        </div>
        <img :src="reviewImg" style="width:120px;opacity:0.7;margin:24px 0;" />
      </div>
      <template #footer>
        <el-button @click="reviewDialogOpen = false">取消</el-button>
        <el-button type="primary" @click="handleReviewPass">审核通过</el-button>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <AddContract ref="addContractRef" @addContractSuccess="getList" :isShowfarmer="false"></AddContract>
  </div>
</template>

<script setup>
import { getToken } from '@/utils/auth'
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, Download, UploadFilled, Document } from '@element-plus/icons-vue'
import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer } from "@/api/customer/customer"
import { historicalContract } from '@/api/order'
import { useDict } from '@/utils/dict'
import { getEmployeeDropdown } from "@/api/employee/employee"
import useUserStore from '@/store/modules/user'
import Cascader from '@/components/Cascader'
import { deptTreeSelect, bankCardOcr } from "@/api/common";
import AddContract from "@/components/AddContract/index.vue"
const { proxy } = getCurrentInstance()
// 字典数据
const { zkss_review, zkss_customer_type } = useDict('zkss_review', 'zkss_customer_type')
// 获取用户store
const userStore = useUserStore()

// 数据状态
const map = ref(null);
const localsearch = ref('')
const geocoder = ref(null)
const singleCode = ref()
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const total = ref(0)
const customerList = ref([])
const title = ref("")
const open = ref(false)
const employeeDropdownList = ref([])
const deptOptions = ref(undefined);

// 详情弹窗相关
const detailOpen = ref(false)
const detailData = ref({})
const showContractTable = ref(false) // 控制是否显示合同表格

// 营业执照图片预览
const businessLicensePreviewVisible = ref(false)
const businessLicensePreviewUrl = ref('')
const addContractRef = ref(null)
function handleBusinessLicensePreview(url) {
  businessLicensePreviewUrl.value = url
  businessLicensePreviewVisible.value = true
}

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect({ type: "0,1" }).then((response) => {
    deptOptions.value = response.data;
  });
}

// 审核通过弹窗相关
const reviewDialogOpen = ref(false)
const reviewRow = ref(null)
// 审核弹窗图片本地化
import reviewImg from '@/assets/images/review.png'

// 银行卡识别相关
const cardOcrDialogVisible = ref(false)
const cardOcrUploadRef = ref(null)
const cardOcrResult = ref(null)
const selectedCardImage = ref(null)
function openReviewDialog(row) {
  reviewRow.value = row
  reviewDialogOpen.value = true
}
async function handleReviewPass() {
  if (!reviewRow.value) return
  try {
    const updateData = { ...reviewRow.value, reviewStatus: 1 }
    await updateCustomer(updateData)
    ElMessage.success('审核通过')
    reviewDialogOpen.value = false
    getList()
  } catch (e) {
    ElMessage.error('操作失败')
  }
}

const handleView = async (row) => {
  const customerId = row.customerId || ids.value[0]
  try {
    const response = await getCustomer(customerId)
    Object.assign(detailData.value, response.data)
    showContractTable.value = false // 查看详情时不显示合同表格
    nextTick(() => {
      detailOpen.value = true
    })
  } catch (error) {
    console.error(error)
  }
}

// 合同历史数据
const contractHistoryList = ref([])

const handleViewContract = async (row) => {
  const customerId = row.customerId || ids.value[0]
  try {
    const response = await getCustomer(customerId)
    Object.assign(detailData.value, response.data)
    showContractTable.value = true // 查看合同时显示合同表格

    // 获取合同历史数据
    const response1 = await historicalContract({
      id: row.customerId,
      customerType: row.customerType
    })
    contractHistoryList.value = response1

    nextTick(() => {
      detailOpen.value = true
    })
  } catch (error) {
    ElMessage.error('获取合同详情失败')
  }
}

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  customerType: null,
  nameOrNumber: null,
  reviewStatus: null,
})

// 表单参数
const form = reactive({
  customerId: null,
  customerType: null,
  customerName: null,
  contactPerson: null,
  phoneNumber: null,
  urgentContactPerson: null,
  urgentPhoneNumber: null,
  region: null,
  address: null,
  lat: null,
  lng: null,
  employeeId: null,
  businessLicense: null,
  householdName: null,
  cardBankNumber: null,
  openingBank: null,
  wxOpenId: null,
  reviewStatus: null,
  delFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null,
  deptId: null,
  businessLicenseList: null,
})

// 表单校验
const rules = reactive({
  deptId: [{ required: true, message: "所属组织不能为空", trigger: "change" }],
  customerType: [
    { required: true, message: "客户类型不能为空", trigger: "change" }
  ],
  customerName: [
    { required: true, message: "客户名称不能为空", trigger: "blur" },
    { max: 50, message: "客户名称长度不能超过50个字符", trigger: "blur" }
  ],
  contactPerson: [
    { required: true, message: "联系人不能为空", trigger: "blur" },
    { max: 20, message: "联系人长度不能超过20个字符", trigger: "blur" }
  ],
  phoneNumber: [
    { required: true, message: "联系电话不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的联系电话", trigger: "blur" },
    { max: 11, message: "联系电话长度不能超过11个字符", trigger: "blur" }
  ],
  urgentContactPerson: [
    { required: true, message: "紧急联系人不能为空", trigger: "blur" },
    { max: 20, message: "紧急联系人长度不能超过20个字符", trigger: "blur" }
  ],
  urgentPhoneNumber: [
    { required: true, message: "紧急联系电话不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的联系电话", trigger: "blur" },
    { max: 11, message: "紧急联系电话长度不能超过11个字符", trigger: "blur" }
  ],
  region: [
    { required: true, message: "所属区域不能为空", trigger: "change" },
  ],
  address: [
    { max: 100, message: "详细地址长度不能超过100个字符", trigger: "blur" }
  ],
  employeeId: [
    { required: true, message: "请选择关联业务员", trigger: "change" }
  ],
  householdName: [
    { max: 30, message: "户名长度不能超过30个字符", trigger: "blur" }
  ],
  cardBankNumber: [
    { max: 20, message: "银行卡号长度不能超过20个字符", trigger: "blur" }
  ],
  openingBank: [
    { max: 100, message: "开户银行长度不能超过100个字符", trigger: "blur" }
  ]
})

// 表单引用
const formRef = ref(null)
const queryFormRef = ref(null)

/** 查询客户列表 */
const getList = async () => {
  loading.value = true
  try {
    const response = await listCustomer(queryParams)
    customerList.value = response.records
    total.value = response.total
  } finally {
    loading.value = false
  }
}

// 表单重置
const reset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.customerId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 获取空表单对象
const getEmptyForm = () => ({
  customerId: null,
  customerType: null,
  customerName: null,
  contactPerson: null,
  phoneNumber: null,
  urgentContactPerson: null,
  urgentPhoneNumber: null,
  region: null,
  address: null,
  lat: null,
  lng: null,
  employeeId: null,
  businessLicense: null,
  householdName: null,
  cardBankNumber: null,
  openingBank: null,
  wxOpenId: null,
  reviewStatus: null,
  delFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null,
  deptId: null,
  businessLicenseList: null,
})

/** 新增按钮操作 */
const handleAdd = () => {
  singleCode.value = null
  // 1. 彻底清空内容
  Object.assign(form, getEmptyForm())
  // 2. 设置部门ID
  form.deptId = userStore.deptId || null
  // 3. 先加载下拉数据，加载完再弹窗
  fetchEmployeeDropdown().then(() => {
    // 再次确保下拉选项和内容都为 null
    form.customerType = null
    form.employeeId = null
    open.value = true
    title.value = "添加客户"
    getDeptTree();
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate()
      }
      initMap();
    });
  })
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset()
  const customerId = row.customerId || ids.value[0]
  try {
    const response = await getCustomer(customerId)
    Object.assign(form, response.data)
    singleCode.value = response.data && response.data.regionCascadeId
      ? response.data.regionCascadeId.split(",")
      : [];
    fetchEmployeeDropdown()
    open.value = true
    title.value = "修改客户"
    getDeptTree();
    nextTick(() => {
      initMap();
    });
  } catch (error) {
    console.error(error)
  }
}

/** 提交按钮 */
const submitForm = async () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      singleCode.value = null
      if (form.customerId != undefined) {
        updateCustomer(form).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addCustomer(form).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

// 取消按钮
const cancel = () => {
  open.value = false
  Object.assign(form, getEmptyForm())
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const customerIds = row.customerId || ids.value
  try {
    await ElMessageBox.confirm(`是否确认删除客户姓名为"${row.customerName}"的数据项？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delCustomer(customerIds)
    ElMessage.success("删除成功")
    getList()
  } catch (error) {
    if (error != "cancel") {
      ElMessage.error("删除失败")
    }
  }
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.download('customerManagement/customer/export', {
    ...queryParams
  }, `customer_${new Date().getTime()}.xlsx`)
}


// 文件上传相关
const uploadRef = ref(null)
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/customerManagement/customer/importData",
  updateSupport: 0
})

/** 导入 */
function handleImport() {
  upload.title = "客户导入"
  upload.open = true
}

/** 导入模板 */
const importTemplate = () => {
  proxy.download("customerManagement/customer/importTemplate", {}, `客户导入模板_${new Date().getTime()}.xlsx`)
}

/** 导入相关方法 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs["uploadRef"].handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}

/** 查询业务员 */
const fetchEmployeeDropdown = () => {
  return getEmployeeDropdown({ deptId: userStore.deptId, jobType: 0 }).then(res => {
    employeeDropdownList.value = res.data || []
  })
}

onMounted(() => {
  getList()
})

function getCustomerTypeLabel(type) {
  const item = zkss_customer_type.value.find(d => d.value == type)
  return item ? item.label : type
}


//选择区域
function handleAreaChangeSingle(value) {
  if (value != null && value != '') {
    form.region = value[value.length - 1]
  }

}

//初始化地图
function initMap() {
  if (map.value) {
    map.value.clearOverLays()
  } else {
    var imageURL =
      "https://t0.tianditu.gov.cn/vec_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    const imageURLT =
      "https://t0.tianditu.gov.cn/cia_w/wmts?" +
      "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
      "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
      "&tk=73c8efce00c892bd7f903b0dd59d2ff5";
    var lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
    var lay2 = new T.TileLayer(imageURLT, { minZoom: 1, maxZoom: 18 });
    const config = { layers: [lay, lay2] };
    map.value = new T.Map("mapDiv");
    map.value.enableScrollWheelZoom();
    map.value.enableDrag()
    geocoder.value = new T.Geocoder();
    map.value.addEventListener("click", MapClick);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function (position) {
          map.value.centerAndZoom(new T.LngLat(position.coords.longitude, position.coords.latitude), 14);
        },
        function (error) {
        }
      );
    }
    var searchConfig = {
      pageCapacity: 10,	//每页显示的数量
      onSearchComplete: localSearchResult	//接收数据的回调函数
    };
    //创建搜索对象
    setTimeout(() => {
      localsearch.value = new T.LocalSearch(map.value, searchConfig);
    })
  }

  if (form.lng && form.lat) {
    map.value.centerAndZoom(new T.LngLat(form.lng, form.lat), 14);
    //创建标注对象
    var marker = new T.Marker(new T.LngLat(form.lng, form.lat));
    //向地图上添加标注
    map.value.addOverLay(marker);
  } else {
    map.value.centerAndZoom(new T.LngLat(proxy.centerPointLng, proxy.centerPointLat), 11);
  }
}

//地图点击事件
function MapClick(e) {
  map.value.clearOverLays()
  var marker = new T.Marker(new T.LngLat(e.lnglat.getLng(), e.lnglat.getLat()));
  //向地图上添加标注
  map.value.addOverLay(marker);
  form.lng = e.lnglat.getLng()
  form.lat = e.lnglat.getLat()
  const geocode = new T.Geocoder();
  geocode.getLocation(e.lnglat, function (result) {
    const town = result.addressComponent.town
    const index = result.addressComponent.address.indexOf(town);
    if (index !== -1) {
      form.address = result.addressComponent.address.slice(index + town.length);
    } else {
      form.address = result.addressComponent.poi
    }
  });
}

function localSearchResult(result) {
  map.value.clearOverLays();
  if (result.getPois().length > 0) {
    const resultType = result.resultType
    let center = null
    if (resultType == 1) {
      center = result.pois[0].lonlat
    } else if (resultType == 2) {
      center = result.getStatistics().priorityCitys[0].lonlat
    } else if (resultType == 3) {
      const info = result.getArea()
      center = info.lonlat
    }
    if (center) {
      const arr = center.split(",")
      map.value.panTo(new T.LngLat(arr[0], arr[1]));
      var marker = new T.Marker(new T.LngLat(arr[0], arr[1]));
      map.value.addOverLay(marker);
    }
  }
}

function onSearch() {
  console.log(11111);
  localsearch.value.search(form.address)
}
function handleAddContract(row) {
  addContractRef.value.isShowAddDialog = true
  addContractRef.value.contractForm.farmerId = row.customerId
}
function addContract(){
  addContractRef.value.isShowAddDialog=true
  addContractRef.value.contractForm.farmerId=detailData.value.customerId
}

// 银行卡识别相关方法
function handleRecognizeCard() {
  cardOcrDialogVisible.value = true
  cardOcrResult.value = null
  selectedCardImage.value = null
}

// 处理银行卡图片选择
function handleCardImageChange(file) {
  console.log(file);
  
  // 检查文件对象是否存在
  if (!file || !file.raw) {
    ElMessage.error('文件上传失败，请重试!')
    return false
  }
  
  const isImage = file.raw.type && file.raw.type.startsWith('image/')
  const isLt10M = file.raw.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }

  selectedCardImage.value = file.raw
  uploadCardImage(file.raw)
  return false
}

// 上传银行卡图片进行识别
async function uploadCardImage(file) {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await bankCardOcr(formData)
    
    if (response.code === 200) {
      cardOcrResult.value = response.data
      ElMessage.success('识别成功')
    } else {
      ElMessage.error(response.msg || '识别失败')
    }
  } catch (error) {
    console.error('银行卡识别失败:', error)
    ElMessage.error('识别失败，请重试')
  }
}

// 确认使用识别结果
function confirmCardOcr() {
  if (cardOcrResult.value) {
    // 将识别结果填入表单
    if (cardOcrResult.value.bankCardNumber) {
      form.cardBankNumber = cardOcrResult.value.bankCardNumber
    }
    if (cardOcrResult.value.bankName) {
      form.openingBank = cardOcrResult.value.bankName
    }
    if (cardOcrResult.value.relativeUrl) {
      form.cardBankImage = cardOcrResult.value.relativeUrl
    }
    
    cardOcrDialogVisible.value = false
    ElMessage.success('已应用识别结果')
  }
}
</script>

<style scoped>
/* 合同详情弹窗样式 */
.zkss-detail-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  min-width: 80px;
  margin-right: 12px;
}

.zkss-detail-value {
  font-size: 14px;
  color: #303133;
  flex: 1;
}

/* 历史合同表格样式 */
.el-table .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.el-table .el-table__body td {
  padding: 12px 0;
}

/* 新增按钮徽章样式 */
.el-badge .el-badge__content {
  background-color: #f56c6c;
}
</style>
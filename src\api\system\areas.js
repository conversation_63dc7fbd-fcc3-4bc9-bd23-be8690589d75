import request from '@/utils/request'

// 查询行政区划-省市县乡村五级(2016版本)列表
export function listAreas(query) {
  return request({
    url: '/system/areas/list',
    method: 'get',
    params: query
  })
}

// 查询行政区划-省市县乡村五级(2016版本)详细
export function getAreas(id) {
  return request({
    url: '/system/areas/' + id,
    method: 'get'
  })
}

// 新增行政区划-省市县乡村五级(2016版本)
export function addAreas(data) {
  return request({
    url: '/system/areas',
    method: 'post',
    data: data
  })
}

// 修改行政区划-省市县乡村五级(2016版本)
export function updateAreas(data) {
  return request({
    url: '/system/areas',
    method: 'put',
    data: data
  })
}

// 删除行政区划-省市县乡村五级(2016版本)
export function delAreas(id) {
  return request({
    url: '/system/areas/' + id,
    method: 'delete'
  })
}

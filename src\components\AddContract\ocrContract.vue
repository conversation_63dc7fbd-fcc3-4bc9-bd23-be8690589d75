<template>
    <div>
        <el-dialog v-model="isShowResult" title="详情" width="1000" @close="close" append-to-body>
            <div class="title">{{ formModel.combinationName }}</div>
            <el-form :model="formModel" ref="formRef">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="订单作物" prop="plantTypeId">
                            <el-select placeholder="全部" v-model="formModel.plantTypeId"  style="width: 100%">
                                <el-option v-for="dict in plant_type" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="服务面积" prop="serviceArea" :rules="[
                            { required: true, message: '请输入服务面积', trigger: 'blur' },
                        ]">
                            <el-input v-model="formModel.serviceArea">
                                <template #append>亩</template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="flatserviceStageList" border style="width: 100%" :span-method="tableSpanMethod"
                    @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" ref="tableRef">
                    <el-table-column label="服务阶段" width="100">
                        <template #default="scope">
                            <span>第{{ convertToChineseNumber(scope.row._groupIndex + 1) }}阶段</span>
                        </template>
                    </el-table-column>
                    <el-table-column type="selection" width="50" :selectable="selectable" /> 
                    <el-table-column label="序号" width="60" align="center">
                      <template #default="scope">
                        {{ scope.$index + 1 }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="serviceContent" label="服务内容" min-width="120" />
                    <el-table-column prop="marketPrice" label="市场价（元/亩）" min-width="120" align="center">
                        <template #default="scope">
                            <el-form-item
                                :prop="`serviceStageList.${scope.row._groupIndex}.serviceStageItemResList.${scope.row._rowIndex}.marketPrice`"
                                style="margin-bottom: 0" :validate-on-rule-change="false"
                                :key="`serviceContent-${scope.row._groupIndex}-${scope.row._rowIndex}`">
                                <el-input v-model="formModel.serviceStageList[scope.row._groupIndex]
                                        .serviceStageItemResList[scope.row._rowIndex].marketPrice
                                    " placeholder="请输入金额" @blur="
                    () =>
                        handlemarketPriceChange(
                            scope.row._groupIndex,
                            scope.row._rowIndex
                        )
                " />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column prop="standard" label="作业标准" min-width="180" />
                </el-table>
                <div class="money-box">
                    <span>合计：</span>
                    <span>市场价金额：{{ formModel.marketPrice }}</span>
                    <div class="discount-box">  
                            <el-form-item label="合同金额：" prop="crop">
                                <el-input v-model="formModel.contractPrice" placeholder="请输入合同金额">
                                    <template #append>元</template>
                                </el-input>
                            </el-form-item> 
                    </div>
                    <div class="discount-box">
                        <span>折扣金额：{{ formModel.discountPrice }}</span>
                        <div :style="{color:newAvailableAmount<0?'red':'#606266'}" class="tip">提示：可用额度{{newAvailableAmount}}元</div>

                    </div>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <!-- <el-button @click="viewMenu">查看套餐</el-button> -->
                </div>
            </template>
        </el-dialog>

        <!-- 引入查看套餐组件 -->
        <!-- <view-menu ref="viewMenuRef"></view-menu> -->
    </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch, defineExpose,defineProps,defineEmits } from "vue";
import { convertToChineseNumber } from '@/utils/index'
// import ViewMenu from '../viewMenu.vue'
const emit = defineEmits(['confirm'])
const { proxy } = getCurrentInstance()
const { plant_type } = proxy.useDict("plant_type")
const props=defineProps({
    availableAmountPercent:{
        type:Number,
        default:0
    },
    lastAvailableAmount:{
        type:Number,
        default:0
    }
})
const isShowResult = ref(false);
const formRef = ref();
const tableRef = ref(null);
const viewMenuRef = ref(null);
const choosedRows = ref([]);
const formModel = ref({});
const flatserviceStageList = ref([]);
// 监听服务面积、选中行和价格的变化，重新计算金额
watch(
    [
        () => formModel.value.serviceArea,
        choosedRows,
        () => flatserviceStageList.value,
    ],
    () => {
        calculatemarketPrice();
    },
    { deep: true }
);

// 监听合同金额的变化，重新计算折扣金额
watch(
    () => formModel.value.contractPrice,
    () => {
        calculatecontractPrice();
    }
);
// 计算可用额度
const newAvailableAmount=computed(()=>{
    return ((parseFloat(formModel.value.marketPrice)*props.availableAmountPercent*0.01)-parseFloat(formModel.value.discountPrice)+parseFloat(props.lastAvailableAmount)).toFixed(2)
   
})
// 计算市场价金额
function calculatemarketPrice() {
    console.log(1266666,choosedRows.value);
    let totalmarketPrice = 0;
    // 获取选中行的ID
    const choosedIds = choosedRows.value.map((row) => row.id);

    // 从原始表格数据中获取最新价格
    formModel.value.serviceStageList.forEach((group) => {
        group.serviceStageItemResList.forEach((item) => {
            // 如果该项被选中
            if (choosedIds.includes(item.id)) {
                // 确保价格是数字
                const marketPrice = parseFloat(item.marketPrice) || 0;
                totalmarketPrice += marketPrice;
            }
        });
    });
    // 计算市场价金额 = 价格总和 * 服务面积
    const area = parseFloat(formModel.value.serviceArea) || 0;
    console.log(144444,totalmarketPrice,area)
    formModel.value.marketPrice = (totalmarketPrice * area).toFixed(2);

    // 同时更新合同金额
    calculatecontractPrice();
}

// 计算折扣金额（合同金额为输入项，折扣金额=市场价金额-合同金额）
function calculatecontractPrice() {
    // 合同金额为输入项
    const marketPrice = parseFloat(formModel.value.marketPrice) || 0;
    const contractPrice = parseFloat(formModel.value.contractPrice) || 0;
    formModel.value.discountPrice = (marketPrice - contractPrice).toFixed(2);
}

onMounted(() => { });
function getFlatserviceStageList() {
    const arr = [];
    formModel.value.serviceStageList.forEach((group, groupIdx) => {
        group.serviceStageItemResList.forEach((item, rowIdx) => {
            // 直接在原始item上挂载辅助字段，保证marketPrice和choosedRows同步
            item.stage = rowIdx === 0 ? group.stage : "";
            item._groupIndex = groupIdx;
            item._rowIndex = rowIdx;
            item._isFirstInGroup = rowIdx === 0;
            item._groupCount = group.serviceStageItemResList.length;
            arr.push(item); // 直接push引用
        });
    });
    flatserviceStageList.value = arr;
    setDefaultSelection();
}
function setDefaultSelection() {
    nextTick(() => {
        // 确保tableRef.value已初始化
        if (tableRef.value) {
            // 先清除所有选择，避免重复勾选
            tableRef.value.clearSelection();

            // 遍历所有行，勾选choosed为true的行
            flatserviceStageList.value.forEach((row) => {
                if (row.choosed) {
                    tableRef.value.toggleRowSelection(row, true);
                }
            });

            // 初始化时同步选中状态到choosedRows
            const choosedItems = flatserviceStageList.value.filter(
                (item) => item.choosed
            );
            choosedRows.value = choosedItems; 
            // 初始计算市场价金额
            calculatemarketPrice();
        }
    });
}

// 合并"服务阶段"单元格
function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 只合并第一列（服务阶段）
  if (columnIndex === 0) {
    if (row._rowIndex === 0) {
      return {
        rowspan: row._groupCount,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
}

function selectable(row) {
    return true;
}

function tableRowClassName({ row }) {
    return "";
}

function handleSelectionChange(val) {
    choosedRows.value = val;
    // 选择变化时重新计算市场价金额
    calculatemarketPrice();
}

function handlemarketPriceChange(groupIdx, rowIdx) {
    if (formRef.value) {
        formRef.value.clearValidate(
            `serviceStageList.${groupIdx}.serviceStageItemResList.${rowIdx}.marketPrice`
        );
        formRef.value.validateField(
            `serviceStageList.${groupIdx}.serviceStageItemResList.${rowIdx}.marketPrice`
        );
    }
    // 价格变化时重新计算市场价金额
    calculatemarketPrice();
}

// 查看套餐
function viewMenu() {
    if (viewMenuRef.value) {
        // 准备要传递的数据
        const menuData = {
            serviceArea: formModel.value.serviceArea,
            marketPrice: formModel.value.marketPrice,
            serviceStageList: JSON.parse(
                JSON.stringify(formModel.value.serviceStageList)
            ), // 深拷贝避免引用问题
        };

        // 调用viewMenu组件的setMenuData方法传递数据
        viewMenuRef.value.setMenuData(menuData);
    }
}

function resetData() {
    choosedRows.value = [];
    formModel.value.serviceStageList = [];
    flatserviceStageList.value = [];
    formModel.value.marketPrice = 0;
    formModel.value.discountPrice = 0;
    formModel.value.contractPrice = 0;
    formModel.value.plantTypeId = "";
    formModel.value.serviceArea = "";
}
function close() {
    isShowResult.value = false;
}

function submitForm() {
    if (formRef.value) {
        formRef.value.validate((valid) => {
            if (valid) {
                let isEmpty=false
               for(let i=0;i<choosedRows.value.length;i++){
                if(!choosedRows.value[i].marketPrice ){
                    console.log(i,choosedRows.value[i])
                    isEmpty=true
                    break
                }
               }
               if(isEmpty){
                proxy.$modal.msgError("请输入市场价")
                return
               }
                // 根据选中状态更新choosed属性
                const ids = choosedRows.value.map((item) => item.id);
                flatserviceStageList.value.forEach((item) => {
                    if (ids.includes(item.id)) {
                        item.choosed = true;
                    } else {
                        item.choosed = false;
                    }
                });
                formModel.value.serviceStageList.forEach((item) => {
                    item.serviceStageItemResList.forEach((child) => { 
                        if (ids.includes(child.id)) {
                            child.choosed = true;
                        } else {
                            child.choosed = false;
                        }
                    });
                }); 
                // 生成新的newserviceStageList，只包含choosed为true的项
                let newserviceStageList = [];
                formModel.value.serviceStageList.forEach((group, groupIdx) => {
                    // 筛选出选中的子项
                    const choosedserviceStageItemResList =
                        group.serviceStageItemResList.filter((child) => child.choosed);

                    // 如果该分组有选中的子项，则创建新的分组
                    if (choosedserviceStageItemResList.length > 0) {
                        // 创建新的分组对象
                        const newGroup = {
                            stage: group.stage,
                            serviceStageItemResList: choosedserviceStageItemResList,
                        };

                        // 将新分组添加到结果数组
                        newserviceStageList.push(newGroup);
                    }
                }); 
                // 生成扁平化的结果数据，格式与flatserviceStageList一致
                let flatNewserviceStageList = [];
                newserviceStageList.forEach((group, groupIdx) => {
                    group.serviceStageItemResList.forEach((item, rowIdx) => {
                        flatNewserviceStageList.push({
                            ...item,
                            stage: rowIdx === 0 ? group.stage : "",
                            _groupIndex: groupIdx,
                            _rowIndex: rowIdx,
                            _isFirstInGroup: rowIdx === 0,
                            _groupCount: group.serviceStageItemResList.length,
                        });
                    });
                }); 
            
                const obj={
                    allForm:formModel.value,
                    newserviceStageList:newserviceStageList,
                    flatNewserviceStageList:flatNewserviceStageList,
                    newAvailableAmount:newAvailableAmount.value
                }  
                emit('confirm',obj)
                isShowResult.value=false
                // console.log("表单数据", formModel.value.serviceStageList);
                // console.log("选中行", choosedRows.value);
                // console.log("新生成的数据结构", newserviceStageList);
                // console.log("新生成的扁平数据", flatNewserviceStageList);
                // 这里可以添加提交逻辑
            }
        });
    }
}

function cancel() {
    close();
}
defineExpose({ isShowResult, formModel, getFlatserviceStageList });
</script>

<style scoped lang="scss">
.title {
    width: 100%;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}

.money-box {
    display: flex;
    margin-top: 10px;

    &>span {
        margin-right: 30px;
    }

    .discount-box {
        // display: flex;
        margin-right: 30px;

        .tip {
            position: relative;
            top: 10px;
            left: 60px;
        }
    }
}
</style>
<template>
  <div class="cascader-wrapper">
    <el-cascader v-model="internalValue" :options="options" :props="cascaderProps" @change="handleChange"
      @expand-change="handleExpandChange" :placeholder="placeholder" style="width: 100%" clearable :collapse-tags="collapseTags"
      :collapse-tags-tooltip="collapseTagsTooltip" :multiple="multiple"
      ref="cascaderRef"></el-cascader>
  </div>
</template>

<script setup>
import { ref, defineProps, watch, onMounted, defineEmits, nextTick, shallowRef } from 'vue';
import { dataCity } from "@/api/common";

const props = defineProps({
  // 选中的值
  modelValue: {
    type: [String, Array],
    default: () => []
  },
  // 占位符
  placeholder: {
    type: String,
    default: "请选择地区"
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 最大加载级别（从1开始）
  maxLoadLevel: {
    type: Number,
    default: 5
  },
  // 最大选择级别（从1开始）
  maxSelectLevel: {
    type: Number,
    default: 5
  },
  collapseTags: {
    type: Boolean,
    default: false
  },
  collapseTagsTooltip: {
    type: Boolean,
    default: false
  },
  // 是否只允许选择叶子节点
  onlyLeaf: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否可以选择任意一级
  checkStrictly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const cascaderRef = ref(null);
const internalValue = ref([]);
const options = shallowRef([]); // 使用shallowRef减少响应式开销
const isInitializing = ref(false);
const isUpdating = ref(false);
const lastValidValue = ref([]); // 记录最后一次有效的值

// 级联选择器配置
const cascaderProps = {
  lazy: true,
  lazyLoad: loadData,
  multiple: props.multiple,
  checkStrictly: props.checkStrictly, // 动态控制
  emitPath: true,
  value: 'id',
  label: 'name',
  leaf: 'isLeaf',
  collapseTags: props.collapseTags,
  collapseTagsTooltip:props.collapseTagsTooltip
};

// 懒加载数据函数
async function loadData(node, resolve) {
  const { level, value } = node;

  // 限制加载级别
  if (level >= props.maxLoadLevel) {
    resolve([]);
    return;
  }

  try {
    const parentId = value || '';
    const response = await dataCity({ areaId: parentId });
    const data = response?.data || [];

    const formattedData = data.map(item => ({
      id: item.id,
      name: item.name,
      isLeaf: item.isLeaf === 1 || level >= props.maxLoadLevel - 1,
      level: level + 1,
      grade: item.grade,
      fullName: item.fullName
    }));

    resolve(formattedData);
  } catch (error) {
    console.error('加载地区数据失败:', error);
    resolve([]);
  }
}

// 预加载数据用于回显
async function preloadDataForDisplay(selectedPaths) {
  if (!selectedPaths || selectedPaths.length === 0) return;

  isInitializing.value = true;

  try {
    for (const path of selectedPaths) {
      if (Array.isArray(path) && path.length > 0) {
        for (let i = 0; i < path.length; i++) {
          const currentPath = path.slice(0, i + 1);
          await loadDataForPath(currentPath);
        }
      }
    }
  } catch (error) {
    console.error('预加载数据失败:', error);
  } finally {
    isInitializing.value = false;
  }
}

// 为指定路径加载数据
async function loadDataForPath(path) {
  if (path.length === 0) return;

  try {
    const parentId = path[path.length - 1];
    const response = await dataCity({ areaId: parentId });
    const data = response?.data || [];

    const formattedData = data.map(item => ({
      id: item.id,
      name: item.name,
      isLeaf: item.isLeaf === 1 || path.length >= props.maxLoadLevel - 1,
      level: path.length + 1,
      grade: item.grade,
      fullName: item.fullName
    }));

    updateOptionsData(path, formattedData);
  } catch (error) {
    console.error('加载路径数据失败:', error);
  }
}

// 更新options数据结构
function updateOptionsData(path, data) {
  if (path.length === 0) {
    options.value = data;
    return;
  }

  let currentLevel = options.value;

  for (let i = 0; i < path.length - 1; i++) {
    const nodeId = path[i];
    let parentNode = currentLevel.find(item => item.id === nodeId);

    if (!parentNode) {
      parentNode = {
        id: nodeId,
        name: `加载中...`,
        isLeaf: false,
        level: i + 1,
        children: []
      };
      currentLevel.push(parentNode);
    }

    if (!parentNode.children) {
      parentNode.children = [];
    }

    currentLevel = parentNode.children;
  }

  const lastNodeId = path[path.length - 1];
  let lastNode = currentLevel.find(item => item.id === lastNodeId);

  if (!lastNode) {
    lastNode = {
      id: lastNodeId,
      name: `加载中...`,
      isLeaf: false,
      level: path.length,
      children: []
    };
    currentLevel.push(lastNode);
  }

  lastNode.children = data;
}

// 处理选择变化
const handleChange = (value) => {
  console.log('选择的值:', value);

  if (isUpdating.value) return;

  let finalValue = value;

  // 如果设置了只允许选择叶子节点
  if (props.onlyLeaf && value) {
    const filteredValue = value.filter(item => {
      return isLeafNode(item);
    });

    if (filteredValue.length !== value.length) {
      finalValue = filteredValue;
      internalValue.value = filteredValue;
    }
  }

  // 限制选择级别
  if (props.maxSelectLevel > 0) {
    const limitedValue = limitSelectLevel(finalValue);
    if (limitedValue.length !== finalValue.length) {
      finalValue = limitedValue;
      internalValue.value = limitedValue;
    }
  }

  // 记录有效值
  lastValidValue.value = finalValue;

  // 触发事件
  emit('update:modelValue', finalValue);
  emit('change', finalValue);
};

// 处理展开变化
const handleExpandChange = (value) => {
  console.log('展开的节点:', value);
};

// 判断是否为叶子节点
function isLeafNode(path) {
  if (!Array.isArray(path)) return false;

  // 根据级别判断
  if (path.length >= props.maxSelectLevel) return true;

  // 根据isLeaf字段判断（如果有的话）
  return false;
}

// 限制选择级别
function limitSelectLevel(value) {
  if (!Array.isArray(value)) return value;

  return value.filter(item => {
    if (Array.isArray(item)) {
      return item.length <= props.maxSelectLevel;
    }
    return true;
  });
}

// 监听外部传入的值变化
watch(() => props.modelValue, async (newValue) => {
  if (isUpdating.value) return;

  let processedValue = [];

  // 处理输入值格式
  if (newValue) {
    if (typeof newValue === 'string') {
      // 如果是字符串，按逗号分割
      processedValue = newValue.split(',').filter(item => item.trim());
    } else if (Array.isArray(newValue)) {
      // 如果是数组，直接使用
      processedValue = [...newValue];
    }
  }

  // 只有当值真正改变时才更新
  if (JSON.stringify(processedValue) !== JSON.stringify(internalValue.value)) {
    internalValue.value = processedValue;
    lastValidValue.value = processedValue;

    if (processedValue.length > 0 && !isInitializing.value) {
      await preloadDataForDisplay(processedValue);
    }
  }
}, { immediate: true, deep: true });

// 组件挂载时初始化
onMounted(async () => {
  if (props.modelValue && (Array.isArray(props.modelValue) ? props.modelValue.length > 0 : props.modelValue)) {
    console.log('初始化级联选择器，初始值:', props.modelValue);
    await preloadDataForDisplay(Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]);
  }
});

// 暴露方法给父组件
defineExpose({
  // 清空选择
  clear: () => {
    internalValue.value = [];
    emit('update:modelValue', []);
  },
  // 设置值
  setValue: (value) => {
    internalValue.value = value;
    emit('update:modelValue', value);
  },
  // 获取当前值
  getValue: () => {
    return internalValue.value;
  }
});
</script>

<style scoped lang="scss">
.cascader-wrapper {
  position: relative;

  :deep(.el-cascader) {
    .el-cascader__tags {
      .el-tag {
        margin-right: 4px;
        margin-bottom: 4px;
      }
    }
  }

  // 禁用所有过渡动画
  :deep(.el-cascader__dropdown) {
    transition: none !important;
    animation: none !important;
  }

  :deep(.el-cascader-panel) {
    transition: none !important;
    animation: none !important;
  }

  :deep(.el-cascader-node) {
    transition: none !important;
    animation: none !important;
  }

  :deep(.el-cascader-node__label) {
    transition: none !important;
    animation: none !important;
  }

  :deep(.el-cascader__tags) {
    transition: none !important;
    animation: none !important;
  }

  :deep(.el-tag) {
    transition: none !important;
    animation: none !important;
  }
}

// 全局样式覆盖
:deep(.el-cascader__dropdown) {
  transition: none !important;
  animation: none !important;
}

:deep(.el-cascader-panel) {
  transition: none !important;
  animation: none !important;
}
</style>

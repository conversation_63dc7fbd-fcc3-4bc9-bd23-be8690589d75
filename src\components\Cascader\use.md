<template>
  <div class="test-single-cascader-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>级联选择器测试 - 单级选择器</span>
        </div>
      </template>
      
      <div class="test-section">
        <h3>1. 基础单选</h3>
        <SingleCascader 
          v-model="selectedArea1"
          placeholder="请选择地区"
          :maxLoadLevel="3"
          :maxSelectLevel="3"
          @change="handleAreaChange1"
        />
        <div class="result-display">
          <strong>选中值:</strong> {{ JSON.stringify(selectedArea1, null, 2) }}
        </div>
      </div>

      <div class="test-section">
        <h3>2. 多选模式</h3>
        <SingleCascader 
          v-model="selectedArea2"
          placeholder="请选择多个地区"
          :multiple="true"
          :maxLoadLevel="3"
          :maxSelectLevel="3"
          @change="handleAreaChange2"
        />
        <div class="result-display">
          <strong>选中值:</strong> {{ JSON.stringify(selectedArea2, null, 2) }}
        </div>
      </div>

      <div class="test-section">
        <h3>3. 限制级别测试</h3>
        <div class="config-group">
          <el-form :inline="true">
            <el-form-item label="最大加载级别">
              <el-input-number v-model="maxLoadLevel" :min="1" :max="5" />
            </el-form-item>
            <el-form-item label="最大选择级别">
              <el-input-number v-model="maxSelectLevel" :min="1" :max="5" />
            </el-form-item>
            <el-form-item label="只选叶子节点">
              <el-switch v-model="onlyLeaf" />
            </el-form-item>
          </el-form>
        </div>
        
        <SingleCascader 
          v-model="selectedArea3"
          placeholder="请选择地区"
          :multiple="true"
          :maxLoadLevel="maxLoadLevel"
          :maxSelectLevel="maxSelectLevel"
          :onlyLeaf="onlyLeaf"
          @change="handleAreaChange3"
        />
        <div class="result-display">
          <strong>选中值:</strong> {{ JSON.stringify(selectedArea3, null, 2) }}
        </div>
      </div>

      <div class="test-section">
        <h3>4. 回显测试</h3>
        <div class="button-group">
          <el-button type="primary" @click="setSingleValue">设置单个值</el-button>
          <el-button type="success" @click="setMultipleValues">设置多个值</el-button>
          <el-button type="warning" @click="setStringValue">设置字符串值</el-button>
          <el-button type="info" @click="clearValue">清空值</el-button>
        </div>
        
        <SingleCascader 
          v-model="selectedArea4"
          placeholder="请选择地区"
          :multiple="true"
          :maxLoadLevel="3"
          :maxSelectLevel="3"
          @change="handleAreaChange4"
          ref="cascaderRef"
        />
        <div class="result-display">
          <strong>选中值:</strong> {{ JSON.stringify(selectedArea4, null, 2) }}
        </div>
      </div>

      <div class="test-section">
        <h3>5. 预设测试数据</h3>
        <div class="preset-buttons">
          <el-button 
            v-for="(preset, index) in presetData" 
            :key="index"
            size="small"
            @click="setPresetValue(preset)"
          >
            {{ preset.name }}
          </el-button>
        </div>
        
        <SingleCascader 
          v-model="selectedArea5"
          placeholder="请选择地区"
          :multiple="true"
          :maxLoadLevel="3"
          :maxSelectLevel="3"
          @change="handleAreaChange5"
        />
        <div class="result-display">
          <strong>选中值:</strong> {{ JSON.stringify(selectedArea5, null, 2) }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SingleCascader from '@/components/Cascader/singleIndex.vue';

// 测试数据
const selectedArea1 = ref([]);
const selectedArea2 = ref([]);
const selectedArea3 = ref([]);
const selectedArea4 = ref([]);
const selectedArea5 = ref([]);

// 配置参数
const maxLoadLevel = ref(3);
const maxSelectLevel = ref(3);
const onlyLeaf = ref(false);

// 组件引用
const cascaderRef = ref(null);

// 预设测试数据
const presetData = [
  {
    name: '北京市-市辖区-东城区',
    value: [['110000', '110100', '110101']]
  },
  {
    name: '北京市-市辖区-西城区',
    value: [['110000', '110100', '110102']]
  },
  {
    name: '北京市-市辖区-朝阳区',
    value: [['110000', '110100', '110105']]
  },
  {
    name: '多个地区',
    value: [
      ['110000', '110100', '110101'], // 北京市-市辖区-东城区
      ['110000', '110100', '110102'], // 北京市-市辖区-西城区
      ['110000', '110100', '110105']  // 北京市-市辖区-朝阳区
    ]
  },
  {
    name: '只有二级',
    value: [['110000', '110100']]
  },
  {
    name: '只有一级',
    value: [['110000']]
  },
  {
    name: '字符串格式',
    value: '110000,110100,110101'
  }
];

// 事件处理函数
const handleAreaChange1 = (value) => {
  console.log('基础单选 - 选择变化:', value);
};

const handleAreaChange2 = (value) => {
  console.log('多选模式 - 选择变化:', value);
};

const handleAreaChange3 = (value) => {
  console.log('限制级别测试 - 选择变化:', value);
};

const handleAreaChange4 = (value) => {
  console.log('回显测试 - 选择变化:', value);
};

const handleAreaChange5 = (value) => {
  console.log('预设测试 - 选择变化:', value);
};

// 测试函数
const setSingleValue = () => {
  selectedArea4.value = [['110000', '110100', '110101']];
  console.log('设置单个值: 北京市-市辖区-东城区');
};

const setMultipleValues = () => {
  selectedArea4.value = [
    ['110000', '110100', '110101'], // 北京市-市辖区-东城区
    ['110000', '110100', '110102']  // 北京市-市辖区-西城区
  ];
  console.log('设置多个值');
};

const setStringValue = () => {
  selectedArea4.value = '110000,110100,110101';
  console.log('设置字符串值');
};

const clearValue = () => {
  selectedArea4.value = [];
  console.log('清空值');
};

const setPresetValue = (preset) => {
  selectedArea5.value = preset.value;
  console.log('设置预设值:', preset.name);
};

// 测试组件方法
const testComponentMethods = () => {
  if (cascaderRef.value) {
    console.log('当前值:', cascaderRef.value.getValue());
    cascaderRef.value.setValue([['110000', '110100', '110101']]);
    console.log('设置新值后:', cascaderRef.value.getValue());
  }
};
</script>

<style scoped lang="scss">
.test-single-cascader-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #303133;
    font-size: 16px;
  }
}

.config-group {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.button-group {
  margin-bottom: 15px;
  
  .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

.preset-buttons {
  margin-bottom: 15px;
  
  .el-button {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.result-display {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;

  strong {
    color: #409eff;
  }
}
</style> 
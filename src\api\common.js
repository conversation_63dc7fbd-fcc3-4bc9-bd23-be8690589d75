import request from '@/utils/request'

// 查询地区列表
export function dataCity(params) {
  return request({
    url: '/system/areas/areaList',
    method: 'get',
    params
  })
}

// 查询部门下拉树结构
export function deptTreeSelect(query) {
  return request({
    url: '/system/dept/getDeptTree',
    method: 'get',
    params: query
  })
}

// 银行卡识别
export function bankCardOcr(formData) {
  return request({
    url: '/miniProgram/customer/bankCardOcr',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}